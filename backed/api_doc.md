# 后端 API 文档

本文档提供了 `backed` 目录下所有 API 的详细信息。

**基础 URL**: `http://<your_server_ip>:8000`
启动服务器 

.\venv\Scripts\activate
uvicorn main:app --host 0.0.0.0 --port 8000

---

## 1. 机械臂 API (`/robot-arm`)

用于控制机械臂的原始指令。

### 1.1 打开端口

- **Endpoint**: `/robot-arm/open`
- **Method**: `GET`
- **说明**: 初始化机械臂并打开指定串口，成功后返回一个 `handle`（资源号），后续操作都需要此 `handle`。
- **Query Parameters**:
  - `port` (string, required): 串口号，例如 `COM4`。
- **Success Response (200 OK)**:
  ```json
  {
    "handle": "2"
  }
  ```
- **Example**:
  ```bash
  curl -X GET "http://127.0.0.1:8000/robot-arm/open?port=COM4"
  ```

### 1.2 移动

- **Endpoint**: `/robot-arm/move`
- **Method**: `GET`
- **说明**: 控制机械臂移动到指定的 (X, Y) 坐标。
- **Query Parameters**:
  - `handle` (string, required): 从 `/open` 接口获取的资源号。
  - `x` (integer, required): X 轴坐标。
  - `y` (integer, required): Y 轴坐标。
- **Success Response (200 OK)**:
  ```json
  {
    "result": "OK" 
  }
  ```
- **Example**:
  ```bash
  curl -X GET "http://127.0.0.1:8000/robot-arm/move?handle=2&x=150&y=200"
  ```

### 1.3 按下

- **Endpoint**: `/robot-arm/press`
- **Method**: `GET`
- **说明**: 控制触控笔下降，模拟按压动作。
- **Query Parameters**:
  - `handle` (string, required): 资源号。
  - `z` (integer, optional, default: `6`): 下降距离（单位mm）。
- **Success Response (200 OK)**:
  ```json
  {
    "result": "OK"
  }
  ```
- **Example**:
  ```bash
  curl -X GET "http://127.0.0.1:8000/robot-arm/press?handle=2&z=5"
  ```

### 1.4 抬起

- **Endpoint**: `/robot-arm/release`
- **Method**: `GET`
- **说明**: 控制触控笔抬起。
- **Query Parameters**:
  - `handle` (string, required): 资源号。
- **Success Response (200 OK)**:
  ```json
  {
    "result": "OK"
  }
  ```
- **Example**:
  ```bash
  curl -X GET "http://127.0.0.1:8000/robot-arm/release?handle=2"
  ```

### 1.5 复位

- **Endpoint**: `/robot-arm/reset`
- **Method**: `GET`
- **说明**: 控制机械臂回到原点 (X0, Y0, Z0)。
- **Query Parameters**:
  - `handle` (string, required): 资源号。
- **Success Response (200 OK)**:
  ```json
  {
    "result": "OK"
  }
  ```
- **Example**:
  ```bash
  curl -X GET "http://127.0.0.1:8000/robot-arm/reset?handle=2"
  ```

### 1.6 关闭端口

- **Endpoint**: `/robot-arm/close`
- **Method**: `GET`
- **说明**: 关闭串口并释放资源。
- **Query Parameters**:
  - `handle` (string, required): 资源号。
- **Success Response (200 OK)**:
  ```json
  {
    "result": "OK"
  }
  ```
- **Example**:
  ```bash
  curl -X GET "http://127.0.0.1:8000/robot-arm/close?handle=2"
  ```

---

## 2. YOLO 图像识别 API (`/yolo`)

用于图像的目标检测。

### 2.1 目标检测

- **Endpoint**: `/yolo/detect`
- **Method**: `POST`
- **说明**: 上传一张图片，服务器使用 YOLO 模型进行分析，并返回检测到的所有目标信息。图片直接在内存中处理，**只有当检测到的目标数量小于等于1时**，原始图片才会被保存到服务器的 `backed/yolo_detected_images` 目录下。
- **Request Body**: `multipart/form-data`
  - `file` (binary, required): 需要检测的图片文件。
- **Success Response (200 OK)**:
  ```json
  {
    "results": [
      {
        "class_id": 1,
        "class_name": "person",
        "confidence": 0.95,
        "bbox": [150.5, 200.0, 350.5, 600.0]
      },
      {
        "class_id": 15,
        "class_name": "cat",
        "confidence": 0.88,
        "bbox": [400.0, 450.0, 550.0, 580.0]
      }
    ]
  }
  ```
  - `results`: 一个包含所有检测到目标的列表。
    - `class_id`: 类别ID。
    - `class_name`: 类别名称。
    - `confidence`: 置信度 (0.0 - 1.0)。
    - `bbox`: 边界框坐标 `[x_min, y_min, x_max, y_max]`。
- **Example**:
  ```bash
  curl -X POST "http://127.0.0.1:8000/yolo/detect" -F "file=@/path/to/your/image.jpg"
  ```
- **Error Response (500 Internal Server Error)**:
  ```json
  {
    "error": "详细错误信息..."
  }
  ``` 