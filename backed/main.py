from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from robot_arm_api import router as robot_arm_router
from yolo_api import router as yolo_router
from websocket_api import router as websocket_router

app = FastAPI()

# 添加CORS中间件支持跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(robot_arm_router)
app.include_router(yolo_router)
app.include_router(websocket_router)
