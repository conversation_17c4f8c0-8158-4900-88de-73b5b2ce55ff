from fastapi import APIRouter, Query, HTTPException
import requests
import json
import os

router = APIRouter(prefix="/robot-arm", tags=["robot-arm"])

# 机械臂服务地址（仅 http，不带 /MyWcfService/getstring）
ROBOT_ARM_BASE = "http://127.0.0.1:8082"  # 可根据实际情况修改

# 缓存文件路径
CACHE_FILE = "robot_arm_cache.json"

# 端口句柄缓存字典 {port: handle}
port_handle_cache = {}

# 工具函数

def load_cache():
    """从文件加载缓存"""
    global port_handle_cache
    try:
        if os.path.exists(CACHE_FILE):
            with open(CACHE_FILE, 'r', encoding='utf-8') as f:
                port_handle_cache = json.load(f)
    except Exception as e:
        print(f"加载缓存文件失败: {e}")
        port_handle_cache = {}

def save_cache():
    """保存缓存到文件"""
    try:
        with open(CACHE_FILE, 'w', encoding='utf-8') as f:
            json.dump(port_handle_cache, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存缓存文件失败: {e}")

def call_robot_arm(params: dict):
    url = f"{ROBOT_ARM_BASE}/MyWcfService/getstring"
    resp = requests.get(url, params=params, timeout=5)
    return resp.text

@router.get("/open")
def open_handle(port: str = Query(..., description="端口名，如 COM4")):
    params = {"duankou": port, "hco": 0, "daima": 0}
    result = call_robot_arm(params)
    # 去掉 JSON 字符串的引号
    if result.startswith('"') and result.endswith('"'):
        result = result[1:-1]
    
    # 尝试将结果转换为整数进行判断
    try:
        handle_value = int(result)
        if handle_value > 0:
            # 句柄大于0，缓存到本地并返回
            port_handle_cache[port] = result
            save_cache()  # 保存到文件
            return {"handle": result, "success": True}
        else:
            # 句柄等于0，检查是否有缓存的句柄
            if port in port_handle_cache:
                # 有缓存句柄，直接返回缓存的句柄
                return {"handle": port_handle_cache[port], "success": True, "from_cache": True}
            else:
                # 没有缓存，返回错误信息
                raise HTTPException(status_code=400, detail="打开端口失败，请检查端口是否正确或设备是否连接")
    except ValueError:
        # 如果无法转换为整数，检查缓存
        if port in port_handle_cache:
            return {"handle": port_handle_cache[port], "success": True, "from_cache": True}
        else:
            raise HTTPException(status_code=400, detail="打开端口失败，返回值无效")

@router.get("/move")
def move(handle: str, x: int, y: int):
    params = {"duankou": 0, "hco": handle, "daima": f"X{x}Y{y}"}
    result = call_robot_arm(params)
    return {"result": result}

@router.get("/press")
def press(handle: str, z: int = 6):
    params = {"duankou": 0, "hco": handle, "daima": f"Z{z}"}
    result = call_robot_arm(params)
    return {"result": result}

@router.get("/release")
def release(handle: str):
    params = {"duankou": 0, "hco": handle, "daima": "Z0"}
    result = call_robot_arm(params)
    return {"result": result}

@router.get("/reset")
def reset(handle: str):
    params = {"duankou": 0, "hco": handle, "daima": "X0Y0Z0"}
    result = call_robot_arm(params)
    return {"result": result}

@router.get("/close")
def close_handle(handle: str):
    params = {"duankou": 0, "hco": handle, "daima": 0}
    result = call_robot_arm(params)
    
    # 清空对应端口的缓存句柄
    # 遍历缓存找到对应的端口并清除
    port_to_remove = None
    for port, cached_handle in port_handle_cache.items():
        if cached_handle == handle:
            port_to_remove = port
            break
    
    if port_to_remove:
        del port_handle_cache[port_to_remove]
        save_cache()  # 保存到文件
    
    return {"result": result, "cache_cleared": port_to_remove is not None}

# 初始化时加载缓存
load_cache()