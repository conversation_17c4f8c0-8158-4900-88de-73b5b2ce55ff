from fastapi import APIRouter, File, UploadFile, BackgroundTasks
from fastapi.responses import JSONResponse
import os
from ultralytics import <PERSON>OLO
from io import BytesIO
from PIL import Image
import uuid
import asyncio
from concurrent.futures import ThreadPoolExecutor

router = APIRouter(prefix="/yolo", tags=["yolo"])

MODEL_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), 'best.pt'))
SAVE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'yolo_detected_images'))
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10 MB

# 确保模型路径存在
if not os.path.exists(MODEL_PATH):
    raise FileNotFoundError(f"YOLO model not found at {MODEL_PATH}")

model = YOLO(MODEL_PATH)

# 动态设置线程池大小为CPU核心数，以充分利用硬件资源
executor = ThreadPoolExecutor(max_workers=os.cpu_count() or 2)

# 确保保存目录存在
os.makedirs(SAVE_DIR, exist_ok=True)


def process_yolo_detection(image_bytes: bytes):
    """
    在线程池中执行YOLO推理的函数
    """
    # 从内存字节流中打开图片
    image = Image.open(BytesIO(image_bytes)).convert("RGB")
    
    # 使用YOLO模型进行推理
    results = model(image, verbose=False)
    result = results[0]
    boxes = result.boxes
    names = result.names
    
    output = []
    if len(boxes) > 0:
        xyxy_boxes = boxes.xyxy.cpu().numpy()
        labels_indices = boxes.cls.cpu().numpy().astype(int)
        confs = boxes.conf.cpu().numpy()

        for i in range(len(xyxy_boxes)):
            box = xyxy_boxes[i]
            label_index = labels_indices[i]
            conf = float(confs[i])
            category = names.get(label_index, "Unknown")
            x_min, y_min, x_max, y_max = [float(coord) for coord in box]
            
            output.append({
                "class_id": int(label_index),
                "class_name": category,
                "confidence": conf,
                "bbox": [x_min, y_min, x_max, y_max]
            })
    
    return output


def save_image_background(image_bytes: bytes, filename: str):
    """
    后台异步保存图片的函数
    """
    try:
        # 使用UUID确保文件名唯一，避免覆盖
        file_extension = os.path.splitext(filename)[1] if filename and '.' in filename else '.jpg'
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        save_path = os.path.join(SAVE_DIR, unique_filename)
        with open(save_path, "wb") as f:
            f.write(image_bytes)
    except Exception as e:
        print(f"保存图片失败: {e}")


@router.post("/detect")
async def detect(background_tasks: BackgroundTasks, file: UploadFile = File(...)):
    """
    接收图片文件，使用YOLO模型进行对象检测。
    - 图片直接在内存中处理，不写入临时文件。
    - YOLO推理在线程池中异步执行
    - 只有当检测到的目标数量 <= 1 时，才会将原始图片异步保存到服务器的 yolo_detected_images 目录。
    """
    # 0. 校验文件大小
    if file.size > MAX_FILE_SIZE:
        return JSONResponse(
            status_code=413,
            content={"error": f"文件大小不能超过 {MAX_FILE_SIZE / 1024 / 1024}MB"}
        )

    # 1. 将上传的文件读入内存
    image_bytes = await file.read()
    
    try:
        # 2. 在线程池中异步执行YOLO推理
        loop = asyncio.get_event_loop()
        output = await loop.run_in_executor(executor, process_yolo_detection, image_bytes)

        # 3. 如果检测到的目标小于等于1个，则异步保存图片
        if len(output) <= 1:
            background_tasks.add_task(save_image_background, image_bytes, file.filename)

        return JSONResponse(content={"results": output})
        
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})