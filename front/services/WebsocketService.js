import config from "../config.js";

/**
 * WebSocket服务组件
 * 提供简单的on/emit接口，自动处理连接、重连等逻辑
 */
export class WebsocketService {
    constructor() {
        this.websocket = null;
        this.deviceId = '';
        this.connectionStatus = 'disconnected'; // disconnected, connecting, connected
        this.eventListeners = new Map(); // 存储事件监听器
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10; // 增加重连次数
        this.reconnectDelay = 3000;
        this.maxReconnectDelay = 30000; // 最大重连延迟
        this.isManualClose = false;

        // 心跳检测
        this.heartbeatInterval = null;
        this.heartbeatIntervalMs = 30000; // 30秒心跳
        this.heartbeatTimeout = null;
        this.heartbeatTimeoutMs = 10000; // 10秒心跳超时
        this.lastHeartbeatTime = null;

        // 连接质量监控
        this.connectionQuality = {
            latency: 0,
            packetLoss: 0,
            lastPingTime: 0,
            consecutiveFailures: 0
        };

        // 防止重复连接
        this.isConnecting = false;
        this.connectionPromise = null;
    }

    /**
     * 初始化WebSocket连接
     * @param {string} deviceId - 设备ID（必填）
     */
    init(deviceId) {
        if (!deviceId) {
            throw new Error('设备ID不能为空');
        }
        this.deviceId = deviceId;
        this.connect();
    }


    /**
     * 建立WebSocket连接
     */
    async connect() {
        // 防止重复连接
        if (this.isConnecting) {
            console.log('连接正在进行中，等待现有连接完成');
            return this.connectionPromise;
        }

        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            return Promise.resolve();
        }

        this.isConnecting = true;
        this.connectionStatus = 'connecting';
        this._emit('connection_status_changed', {status: 'connecting'});

        this.connectionPromise = new Promise((resolve, reject) => {
            const protocol = config.useSSL ? 'wss' : 'ws';
            const wsUrl = `${protocol}://${config.host}:${config.port}/ws/${this.deviceId}`;
            console.log('正在连接WebSocket:', wsUrl);

            try {
                // 清理旧连接
                this.cleanupConnection();

                this.websocket = new WebSocket(wsUrl);
                this.setupWebSocketEvents(resolve, reject);

                // 连接超时处理
                const connectTimeout = setTimeout(() => {
                    if (this.websocket && this.websocket.readyState === WebSocket.CONNECTING) {
                        this.websocket.close();
                        reject(new Error('连接超时'));
                    }
                }, 10000); // 10秒连接超时

                this.websocket.addEventListener('open', () => {
                    clearTimeout(connectTimeout);
                });

            } catch (error) {
                console.error('WebSocket连接失败:', error);
                this.connectionStatus = 'disconnected';
                this._emit('connection_status_changed', {status: 'disconnected', error});
                this.isConnecting = false;
                this.scheduleReconnect();
                reject(error);
            }
        });

        try {
            await this.connectionPromise;
        } finally {
            this.isConnecting = false;
        }

        return this.connectionPromise;
    }


    /**
     * 解析接收到的 WebSocket 消息
     * @returns {Promise<{json: object, binary: ArrayBuffer|null}>}
     */
    /**
     * 解析 WebSocket 消息（直接操作 Blob，零拷贝优化）
     * @param {MessageEvent<Blob>} event
     * @returns {Promise<{ json: object, blob: Blob | null }>}
     */
    async  parseWebSocketData(event) {
        // 1. 直接利用 Blob 的 stream() API 避免内存复制
        const blob = event.data;
        if (blob.size === 0) return { json: {}, blob: null };

        // 2. 读取前4字节获取头部长度（使用流式读取）
        const headerLengthBuffer = await blob.slice(0, 4).arrayBuffer();
        const headerLength = new DataView(headerLengthBuffer).getUint32(0, false);

        // 3. 读取头部JSON（流式读取，避免加载整个Blob）
        const headerBlob = blob.slice(4, 4 + headerLength);
        const headerText = await headerBlob.text();
        const json = JSON.parse(headerText);

        // 4. 直接返回原始Blob的剩余部分（零拷贝）
        const binaryStart = 4 + headerLength;
        const payloadBlob = binaryStart < blob.size
            ? blob.slice(binaryStart)
            : null;

        return { json, blob: payloadBlob };
    }

    /**
     * 设置WebSocket事件处理
     */
    setupWebSocketEvents(resolve, reject) {
        this.websocket.onopen = () => {
            this.connectionStatus = 'connected';
            this.reconnectAttempts = 0;
            this.connectionQuality.consecutiveFailures = 0;
            console.log('WebSocket连接成功');

            // 启动心跳检测
            this.startHeartbeat();

            this._emit('connection_status_changed', {status: 'connected'});
            this._emit('connected', {deviceId: this.deviceId});

            if (resolve) resolve();
        };

        this.websocket.onmessage = async (event) => {
            try {
                const message = await this.parseWebSocketData(event);

                // 处理心跳响应
                if (message.json.eventName === 'pong') {
                    this.handleHeartbeatResponse(message.json.timestamp);
                    return;
                }

                console.log(message);
                this._emit(message.json.eventName, message);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
                this.connectionQuality.consecutiveFailures++;
            }
        };

        this.websocket.onclose = (event) => {
            this.connectionStatus = 'disconnected';
            console.log('WebSocket连接关闭', event);

            // 停止心跳检测
            this.stopHeartbeat();

            this._emit('connection_status_changed', {status: 'disconnected'});
            this._emit('disconnected', {code: event.code, reason: event.reason});

            // 如果不是手动关闭，则尝试重连
            if (!this.isManualClose) {
                this.scheduleReconnect();
            }

            if (reject && event.code !== 1000) { // 1000 是正常关闭
                reject(new Error(`连接关闭: ${event.reason || event.code}`));
            }
        };

        this.websocket.onerror = (error) => {
            console.error('WebSocket错误详情:', error);
            this.connectionQuality.consecutiveFailures++;

            let errorMessage = 'WebSocket连接错误';
            // 提供更详细的错误信息
            if (error.currentTarget && error.currentTarget.readyState === 3) {
                errorMessage = 'WebSocket连接失败：无法连接到服务器';
            } else if (error.currentTarget && error.currentTarget.url) {
                errorMessage = `WebSocket连接失败：${error.currentTarget.url}`;
            }

            this._emit('error', {error: errorMessage});

            if (reject) reject(new Error(errorMessage));
        };
    }



    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数，停止重连');
            this._emit('reconnect_failed', {attempts: this.reconnectAttempts});
            return;
        }

        this.reconnectAttempts++;

        // 指数退避算法：延迟时间随重连次数增加
        const delay = Math.min(
            this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
            this.maxReconnectDelay
        );

        console.log(`${delay / 1000}秒后尝试第${this.reconnectAttempts}次重连`);

        setTimeout(() => {
            if (this.connectionStatus === 'disconnected' && !this.isManualClose && !this.isConnecting) {
                this._emit('reconnecting', {attempt: this.reconnectAttempts});
                this.connect().catch(error => {
                    console.error('重连失败:', error);
                });
            }
        }, delay);
    }

    /**
     * 启动心跳检测
     */
    startHeartbeat() {
        this.stopHeartbeat(); // 先停止现有的心跳

        this.heartbeatInterval = setInterval(() => {
            this.sendHeartbeat();
        }, this.heartbeatIntervalMs);

        console.log('心跳检测已启动');
    }

    /**
     * 停止心跳检测
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }

        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }
    }

    /**
     * 发送心跳
     */
    sendHeartbeat() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            const timestamp = Date.now();
            this.connectionQuality.lastPingTime = timestamp;

            this.send('ping', { timestamp });

            // 设置心跳超时
            this.heartbeatTimeout = setTimeout(() => {
                console.warn('心跳超时，连接可能已断开');
                this.connectionQuality.consecutiveFailures++;

                // 如果连续失败次数过多，主动断开重连
                if (this.connectionQuality.consecutiveFailures >= 3) {
                    console.error('心跳连续失败，主动断开连接');
                    this.websocket.close();
                }
            }, this.heartbeatTimeoutMs);
        }
    }

    /**
     * 处理心跳响应
     */
    handleHeartbeatResponse(timestamp) {
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }

        // 计算延迟
        const latency = Date.now() - timestamp;
        this.connectionQuality.latency = latency;
        this.connectionQuality.consecutiveFailures = 0;
        this.lastHeartbeatTime = Date.now();

        console.log(`心跳响应: 延迟 ${latency}ms`);
    }

    /**
     * 清理连接资源
     */
    cleanupConnection() {
        this.stopHeartbeat();

        if (this.websocket) {
            // 移除所有事件监听器
            this.websocket.onopen = null;
            this.websocket.onmessage = null;
            this.websocket.onclose = null;
            this.websocket.onerror = null;

            // 如果连接还在，关闭它
            if (this.websocket.readyState === WebSocket.OPEN ||
                this.websocket.readyState === WebSocket.CONNECTING) {
                this.websocket.close();
            }
        }
    }

    /**
     * 监听事件
     * @param {string} eventType - 事件类型
     * @param {Function} callback - 回调函数
     */
    on(eventType, callback) {
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, []);
        }

        const listeners = this.eventListeners.get(eventType);

        // 检查是否已经存在 相同 的回调函数
        const existingIndex = listeners.findIndex(listener => listener === callback);

        if (existingIndex !== -1) {
            // 如果存在相同 的回调，则覆盖（替换）
            listeners[existingIndex] = callback;
        } else {
            // 如果不存在，则添加新的监听器
            listeners.push(callback);
        }
    }


    /**
     * 触发事件
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     */
    _emit(eventType, data = {}) {
        // 触发本地事件监听器
        if (this.eventListeners.has(eventType)) {
            const listeners = this.eventListeners.get(eventType);
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件监听器执行错误 (${eventType}):`, error);
                }
            });
        }
    }

    /**
     * 发送消息到服务器
     * @param {string} eventName - 消息类型
     * @param  jsonData - 消息对象
     * @param binaryData
     */
    async send(eventName, jsonData = {}, binaryData = null) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            // 1. 准备头部 JSON
            jsonData['eventName'] = eventName;
            jsonData['sendDeviceId'] = this.deviceId;
            const headerStr = JSON.stringify(jsonData);
            const headerBuf = new TextEncoder().encode(headerStr);

            // 2. 准备二进制数据（如果有）
            let binaryBuf = new ArrayBuffer(0);
            if (binaryData instanceof Blob) {
                binaryBuf = await binaryData.arrayBuffer();
            } else if (binaryData instanceof ArrayBuffer) {
                binaryBuf = binaryData;
            }

            // 3. 构造完整消息 [4字节头长度][header][binaryData]
            const headerLength = headerBuf.length;
            const message = new Uint8Array(4 + headerLength + binaryBuf.byteLength);

            // 写入头长度（4字节，大端序）
            new DataView(message.buffer).setUint32(0, headerLength, false);

            // 写入头部 JSON
            message.set(headerBuf, 4);

            // 写入二进制数据（如果有）
            if (binaryBuf.byteLength > 0) {
                message.set(new Uint8Array(binaryBuf), 4 + headerLength);
            }

            // 4. 发送
            this.websocket.send(message);
        } else {
            console.warn('WebSocket未连接，无法发送消息:' + eventName);
            this._emit('send_failed', {reason: 'not_connected'});
        }
    }


    /**
     * 手动关闭连接
     */
    close() {
        this.isManualClose = true;
        this.cleanupConnection();
        this.connectionStatus = 'disconnected';
        this.eventListeners.clear();

        // 重置连接状态
        this.reconnectAttempts = 0;
        this.isConnecting = false;
        this.connectionPromise = null;

        console.log('WebSocket连接已手动关闭');
    }

    /**
     * 获取连接质量信息
     */
    getConnectionQuality() {
        return {
            ...this.connectionQuality,
            status: this.connectionStatus,
            reconnectAttempts: this.reconnectAttempts,
            lastHeartbeat: this.lastHeartbeatTime
        };
    }

    /**
     * 检查连接健康状态
     */
    isConnectionHealthy() {
        if (this.connectionStatus !== 'connected') {
            return false;
        }

        // 检查心跳是否正常
        if (this.lastHeartbeatTime) {
            const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeatTime;
            if (timeSinceLastHeartbeat > this.heartbeatIntervalMs * 2) {
                return false;
            }
        }

        // 检查连续失败次数
        if (this.connectionQuality.consecutiveFailures >= 3) {
            return false;
        }

        return true;
    }

}
