@echo off
echo 启动照片尺寸测量系统后端服务...
echo.

cd /d "%~dp0backed"

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

echo.
echo 安装依赖包...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

echo.
echo 启动WebSocket服务...
echo 服务地址: https://localhost:8000
echo WebSocket地址: wss://localhost:8000/ws
echo.
echo 按 Ctrl+C 停止服务
echo.

uvicorn main:app --host 0.0.0.0 --port 8000 --reload --ssl-keyfile ../front/ssl/localhost+3-key.pem --ssl-certfile ../front/ssl/localhost+3.pem

pause
