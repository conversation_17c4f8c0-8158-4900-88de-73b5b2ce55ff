# YOLO标注工具 (YOLO Annotation Tool)

一个基于PyQt6的YOLO目标检测标注工具，支持图像标注、模型训练和自动标注功能。

## 🚀 功能特性

### 核心功能
- **图像标注**: 支持矩形框标注，支持多类别标注
- **模型训练**: 集成YOLOv8训练功能，支持GPU/CPU训练
- **自动标注**: 使用训练好的模型进行自动标注
- **项目管理**: 完整的项目创建、导入、导出功能
- **数据验证**: 自动验证数据集格式和完整性

### 界面特性
- **现代化UI**: 扁平化设计，支持浅色主题
- **响应式布局**: 适配不同屏幕尺寸
- **直观操作**: 鼠标拖拽标注，快捷键支持
- **实时预览**: 标注结果实时显示

## 📋 系统要求

### 最低要求
- Python 3.8+
- Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- 4GB RAM
- 2GB 可用磁盘空间

### 推荐配置
- Python 3.9+
- 8GB+ RAM
- NVIDIA GPU (用于模型训练)
- SSD硬盘

## 🛠️ 安装指南

### 1. 克隆项目
```bash
git clone <repository-url>
cd yolo
```

### 2. 安装依赖
```bash
# 使用pip安装
pip install -r requirements.txt

# 或者使用conda
conda create -n yolo-tool python=3.9
conda activate yolo-tool
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python -m yolo_tool
```

## 📖 使用指南

### 创建新项目

1. **启动程序**后，点击"新建项目"
2. **选择项目路径**和项目名称
3. **添加图像文件夹**，支持jpg、png、bmp等格式
4. **设置类别**，定义要检测的目标类别
5. **开始标注**，使用鼠标拖拽创建标注框

### 图像标注

#### 基本操作
- **创建标注框**: 鼠标拖拽绘制矩形框
- **调整大小**: 拖拽框边缘或角落调整大小
- **移动标注**: 拖拽框中心区域移动位置
- **删除标注**: 选中标注框后按Delete键

#### 快捷键
- `Delete`: 删除选中的标注框
- `Ctrl+Z`: 撤销上一步操作
- `Ctrl+Y`: 重做操作
- `Ctrl+S`: 保存项目
- `Ctrl+O`: 打开项目
- `Ctrl+N`: 新建项目

#### 类别管理
- **添加类别**: 在类别对话框中添加新类别
- **编辑类别**: 双击类别名称进行编辑
- **删除类别**: 选中类别后点击删除按钮
- **类别颜色**: 自动分配不同颜色便于区分

### 模型训练

#### 训练准备
1. **完成标注**: 确保所有图像都已正确标注
2. **验证数据**: 系统会自动验证数据集格式
3. **配置参数**: 根据硬件配置调整训练参数

#### 训练参数说明
- **训练轮数 (Epochs)**: 模型遍历数据集的次数
  - 小数据集(1000张以下): 100-300轮
  - 中等数据集(1000-5000张): 50-150轮
  - 大数据集(5000张以上): 30-100轮

- **批次大小 (Batch Size)**: 每次训练的图片数量
  - 4GB显存: 8
  - 6GB显存: 16
  - 8GB+显存: 32或更大

- **学习率 (Learning Rate)**: 模型权重更新步长
  - 大数据集: 0.001-0.005
  - 小数据集: 0.01-0.02

- **工作线程数 (Workers)**: 数据加载线程数
  - 通常设为CPU核心数的一半

#### 开始训练
1. 点击"开始训练"按钮
2. 系统会自动安装ultralytics库(如需要)
3. 验证数据集格式和完整性
4. 开始训练过程
5. 训练完成后模型保存在`runs/train/`目录

### 自动标注

#### 加载模型
1. 点击"加载模型"按钮
2. 选择训练好的`best.pt`文件
3. 系统会加载模型并准备推理

#### 自动标注
1. 选择要标注的图像文件夹
2. 设置置信度阈值(建议0.5-0.8)
3. 点击"开始自动标注"
4. 系统会自动检测目标并生成标注

#### 标注后处理
- **手动调整**: 可以手动调整自动生成的标注框
- **批量删除**: 可以批量删除低质量的标注
- **类别修正**: 可以修改错误的类别标签

## 🗂️ 项目结构

```
yolo/
├── yolo_tool/                 # 主程序目录
│   ├── __main__.py           # 程序入口
│   ├── main_window.py        # 主窗口
│   ├── style.qss            # 样式文件
│   ├── core/                # 核心功能模块
│   │   ├── project.py       # 项目管理
│   │   ├── bbox.py          # 边界框处理
│   │   ├── inference.py     # 模型推理
│   │   └── system.py        # 系统信息
│   └── widgets/             # 界面组件
│       ├── image_canvas.py  # 图像画布
│       ├── training_dialog.py # 训练对话框
│       └── category_dialog.py # 类别对话框
├── requirements.txt          # 依赖包列表
└── README.md               # 项目说明
```

## 📁 数据格式

### 项目文件结构
```
project_name/
├── images/                  # 图像文件夹
│   ├── train/              # 训练图像
│   └── val/                # 验证图像
├── labels/                 # 标注文件夹
│   ├── train/              # 训练标注
│   └── val/                # 验证标注
├── data.yaml               # 数据集配置文件
└── project.json            # 项目配置文件
```

### 标注格式
- **YOLO格式**: 每行一个目标，格式为`class_id x_center y_center width height`
- **坐标归一化**: 所有坐标值都在0-1范围内
- **类别ID**: 从0开始的整数，对应类别列表中的索引

## 🔧 故障排除

### 常见问题

#### 1. 程序无法启动
- 检查Python版本是否为3.8+
- 确认所有依赖包已正确安装
- 检查PyQt6是否正确安装

#### 2. 图像无法加载
- 确认图像格式支持(jpg, png, bmp等)
- 检查图像文件是否损坏
- 确认图像路径不包含特殊字符

#### 3. 训练失败
- 检查显存是否足够，尝试减小batch_size
- 确认数据集格式正确
- 检查ultralytics库是否正确安装

#### 4. 自动标注效果差
- 增加训练数据量和质量
- 调整置信度阈值
- 检查训练数据标注质量

### 性能优化

#### 内存优化
- 关闭其他占用内存的程序
- 减少同时打开的图像数量
- 使用SSD硬盘提高读写速度

#### GPU训练优化
- 确保CUDA正确安装
- 根据显存大小调整batch_size
- 使用GPU监控工具查看显存使用情况

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境设置
1. Fork项目到你的GitHub账户
2. 克隆你的fork到本地
3. 创建新的功能分支
4. 提交你的更改
5. 创建Pull Request

### 代码规范
- 使用Python PEP 8代码规范
- 添加适当的注释和文档字符串
- 确保所有测试通过

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🙏 致谢

- [Ultralytics](https://github.com/ultralytics/ultralytics) - YOLOv8实现
- [PyQt6](https://www.riverbankcomputing.com/software/pyqt/) - GUI框架
- [OpenCV](https://opencv.org/) - 图像处理库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**注意**: 这是一个开源项目，仅供学习和研究使用。在生产环境中使用前，请确保充分测试。
