import sys
import os
from PyQt6.QtWidgets import QApplication, QStyleFactory

# 将 yolo_tool 的父目录添加到 sys.path
# 这使得我们可以使用 from yolo_tool import ... 这样的导入方式
# current_dir = os.path.dirname(os.path.abspath(__file__))
# parent_dir = os.path.dirname(current_dir)
# if parent_dir not in sys.path:
#     sys.path.insert(0, parent_dir)

from yolo_tool.main_window import YOLOAnnotationTool

def main():
    """
    主函数：
    1. 初始化 QApplication
    2. 创建并显示主窗口
    3. 运行事件循环
    """
    app = QApplication(sys.argv)
    
    # 1. 设置 Fusion 风格，获得更现代、跨平台一致的外观
    app.setStyle(QStyleFactory.create("Fusion"))

    # 2. 加载扁平化QSS样式表
    qss_path = os.path.join(os.path.dirname(__file__), "style.qss")
    try:
        with open(qss_path, "r", encoding="utf-8") as f:
            app.setStyleSheet(f.read())
    except FileNotFoundError:
        # 在找不到样式表时，仍然可以运行，但会使用默认样式
        print(f"警告: 样式文件 'style.qss' 未找到。")

    # 实例化主窗口
    window = YOLOAnnotationTool()
    window.show()

    # 启动 Qt 事件循环
    sys.exit(app.exec())


if __name__ == "__main__":
    # Windows 下禁用DPI缩放，避免界面模糊 (如果需要可以取消注释)
    # if os.name == 'nt':
    #     from ctypes import windll
    #     windll.shcore.SetProcessDpiAwareness(1)
    
    main() 