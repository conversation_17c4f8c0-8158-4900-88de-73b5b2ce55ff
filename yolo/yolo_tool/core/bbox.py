
import sys
import os
import cv2
import yaml
import re
import psutil
import subprocess
import platform
from pypinyin import lazy_pinyin, Style
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QMessageBox, QFileDialog, QLabel, QListWidget, QListWidgetItem, QTreeWidget, 
    QTreeWidgetItem, QSplitter, QComboBox, QDialog, QLineEdit, QDialogButtonBox,
    QAbstractItemView, QScrollArea, QFrame, QGroupBox, QSpinBox, QDoubleSpinBox,
    QTextEdit, QProgressBar, QTabWidget, QFormLayout, QCheckBox
)
from PyQt6.QtGui import QPixmap, QImage, QPainter, QPen, QColor, QFont, QBrush, QIcon
from PyQt6.QtCore import Qt, QPoint, QRect, QSize, QThread, pyqtSignal


class BoundingBox:
    """标注框类 (核心逻辑不变)"""
    def __init__(self, x1: float, y1: float, x2: float, y2: float, class_id: int = 0, class_name: str = ""):
        self.x1, self.y1, self.x2, self.y2 = x1, y1, x2, y2
        self.normalize()
        
        self.class_id = class_id
        self.class_name = class_name
        self.selected = False
        
    def normalize(self):
        """保证 x1 < x2, y1 < y2"""
        if self.x1 > self.x2: self.x1, self.x2 = self.x2, self.x1
        if self.y1 > self.y2: self.y1, self.y2 = self.y2, self.y1

    @property
    def rect(self) -> QRect:
        return QRect(int(self.x1), int(self.y1), int(self.width()), int(self.height()))

    def center_x(self) -> float: return (self.x1 + self.x2) / 2
    def center_y(self) -> float: return (self.y1 + self.y2) / 2
    def width(self) -> float: return self.x2 - self.x1
    def height(self) -> float: return self.y2 - self.y1

    def to_yolo_format(self, img_width: int, img_height: int) -> str:
        center_x = self.center_x() / img_width
        center_y = self.center_y() / img_height
        width = self.width() / img_width
        height = self.height() / img_height
        return f"{self.class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}"

    @classmethod
    def from_yolo_format(cls, line: str, img_width: int, img_height: int, class_names: list):
        parts = line.strip().split()
        if len(parts) != 5: return None
        class_id = int(parts[0])
        center_x = float(parts[1]) * img_width
        center_y = float(parts[2]) * img_height
        width = float(parts[3]) * img_width
        height = float(parts[4]) * img_height
        x1 = center_x - width / 2
        y1 = center_y - height / 2
        x2 = center_x + width / 2
        y2 = center_y + height / 2
        class_name = class_names[class_id] if 0 <= class_id < len(class_names) else f"未知类别 {class_id}"
        return cls(x1, y1, x2, y2, class_id, class_name) 