import os
import cv2
import numpy as np
from typing import List, Tu<PERSON>
from PyQt6.QtCore import QObject, pyqtSignal
import hashlib

class ImageSimilarityProcessor(QObject):
    """图片相似度处理器"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数量
    file_processed = pyqtSignal(str, str, float)  # 源文件, 目标文件, 相似度
    file_deleted = pyqtSignal(str)  # 被删除的文件
    processing_finished = pyqtSignal(int, int)  # 处理的文件数, 删除的文件数
    error_occurred = pyqtSignal(str)  # 错误信息
    
    def __init__(self):
        super().__init__()
        self.similarity_threshold = 0.9  # 相似度阈值
        self.is_processing = False
        self.preview_mode = False  # 预览模式
        
    def set_similarity_threshold(self, threshold: float):
        """设置相似度阈值"""
        self.similarity_threshold = threshold
        
    def get_image_files(self, directory: str) -> List[str]:
        """获取目录下的所有图片文件"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
        image_files = []
        
        try:
            for filename in os.listdir(directory):
                if os.path.splitext(filename)[1].lower() in image_extensions:
                    image_files.append(os.path.join(directory, filename))
        except Exception as e:
            self.error_occurred.emit(f"读取目录失败: {str(e)}")
            
        return image_files
    
    def calculate_image_hash(self, image_path: str) -> str:
        """计算图片的哈希值，用于快速判断完全相同的图片"""
        try:
            with open(image_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def calculate_similarity(self, img1_path: str, img2_path: str) -> float:
        """计算两张图片的相似度"""
        try:
            # 读取图片
            img1 = cv2.imread(img1_path)
            img2 = cv2.imread(img2_path)
            
            if img1 is None or img2 is None:
                return 0.0
            
            # 统一尺寸
            target_size = (224, 224)
            img1_resized = cv2.resize(img1, target_size)
            img2_resized = cv2.resize(img2, target_size)
            
            # 转换为灰度图
            gray1 = cv2.cvtColor(img1_resized, cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(img2_resized, cv2.COLOR_BGR2GRAY)
            
            # 计算结构相似性指数 (SSIM)
            ssim_score = self._calculate_ssim(gray1, gray2)
            
            # 计算直方图相似度
            hist_similarity = self._calculate_histogram_similarity(gray1, gray2)
            
            # 计算特征匹配相似度
            feature_similarity = self._calculate_feature_similarity(img1_resized, img2_resized)
            
            # 综合相似度 (加权平均)
            final_similarity = 0.4 * ssim_score + 0.3 * hist_similarity + 0.3 * feature_similarity
            
            return max(0.0, min(1.0, final_similarity))
            
        except Exception as e:
            self.error_occurred.emit(f"计算相似度失败: {str(e)}")
            return 0.0
    
    def _calculate_ssim(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """计算结构相似性指数"""
        try:
            # 简化的SSIM计算
            mu1 = np.mean(img1)
            mu2 = np.mean(img2)
            sigma1 = np.std(img1)
            sigma2 = np.std(img2)
            sigma12 = np.mean((img1 - mu1) * (img2 - mu2))
            
            c1 = 0.01 ** 2
            c2 = 0.03 ** 2
            
            ssim = ((2 * mu1 * mu2 + c1) * (2 * sigma12 + c2)) / \
                   ((mu1 ** 2 + mu2 ** 2 + c1) * (sigma1 ** 2 + sigma2 ** 2 + c2))
            
            return max(0.0, min(1.0, ssim))
        except:
            return 0.0
    
    def _calculate_histogram_similarity(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """计算直方图相似度"""
        try:
            hist1 = cv2.calcHist([img1], [0], None, [256], [0, 256])
            hist2 = cv2.calcHist([img2], [0], None, [256], [0, 256])
            
            # 归一化直方图
            hist1 = hist1.flatten() / np.sum(hist1)
            hist2 = hist2.flatten() / np.sum(hist2)
            
            # 计算相关性
            correlation = cv2.compareHist(hist1.reshape(1, -1), hist2.reshape(1, -1), cv2.HISTCMP_CORREL)
            
            return max(0.0, min(1.0, (correlation + 1) / 2))
        except:
            return 0.0
    
    def _calculate_feature_similarity(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """计算特征匹配相似度（简化版本以提高性能）"""
        try:
            # 使用ORB特征检测（比SIFT更快）
            orb = cv2.ORB_create(nfeatures=100)  # 限制特征点数量
            
            kp1, des1 = orb.detectAndCompute(img1, None)
            kp2, des2 = orb.detectAndCompute(img2, None)
            
            if des1 is None or des2 is None:
                return 0.0
            
            # 使用BF匹配器（比FLANN更快）
            bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            matches = bf.match(des1, des2)
            
            # 按距离排序
            matches = sorted(matches, key=lambda x: x.distance)
            
            # 计算匹配率
            if len(kp1) > 0 and len(kp2) > 0:
                # 只考虑前50%的匹配
                good_matches = matches[:len(matches)//2] if len(matches) > 0 else []
                match_ratio = len(good_matches) / min(len(kp1), len(kp2))
                return min(1.0, match_ratio)
            else:
                return 0.0
                
        except:
            return 0.0
    
    def process_similarity_comparison(self, source_dir: str, target_dir: str):
        """处理图片相似度比较和删除（UI版本）"""
        if self.is_processing:
            return
            
        self.is_processing = True
        
        try:
            # 获取文件列表
            source_files = self.get_image_files(source_dir)
            target_files = self.get_image_files(target_dir)
            
            if not source_files:
                self.error_occurred.emit("源目录中没有找到图片文件")
                self.is_processing = False
                return
                
            if not target_files:
                self.error_occurred.emit("目标目录中没有找到图片文件")
                self.is_processing = False
                return
            
            # 发送初始进度
            self.progress_updated.emit(0, len(source_files))
            
            # 创建目标文件的哈希缓存
            target_hashes = {}
            for i, target_file in enumerate(target_files):
                if not self.is_processing:  # 检查是否被中断
                    break
                target_hashes[target_file] = self.calculate_image_hash(target_file)
                # 每处理10个文件更新一次进度
                if (i + 1) % 10 == 0:
                    self.progress_updated.emit(0, len(source_files))
            
            deleted_count = 0
            processed_count = 0
            
            for i, source_file in enumerate(source_files):
                if not self.is_processing:  # 检查是否被中断
                    break
                    
                processed_count += 1
                self.progress_updated.emit(processed_count, len(source_files))
                
                # 计算源文件的哈希值
                source_hash = self.calculate_image_hash(source_file)
                
                # 先检查是否有完全相同的文件
                found_duplicate = False
                for target_file, target_hash in target_hashes.items():
                    if source_hash and source_hash == target_hash:
                        self.file_processed.emit(source_file, target_file, 1.0)
                        if not self.preview_mode:
                            self._delete_file(source_file)
                            self.file_deleted.emit(source_file)
                        deleted_count += 1
                        found_duplicate = True
                        break
                
                if found_duplicate:
                    continue
                
                # 如果没有完全相同的文件，进行相似度比较
                max_similarity = 0.0
                most_similar_file = None
                
                # 限制比较的文件数量以提高性能
                max_comparisons = min(50, len(target_files))  # 最多比较50个文件
                comparison_count = 0
                
                for target_file in target_files:
                    if comparison_count >= max_comparisons:
                        break
                        
                    similarity = self.calculate_similarity(source_file, target_file)
                    comparison_count += 1
                    
                    if similarity > max_similarity:
                        max_similarity = similarity
                        most_similar_file = target_file
                    
                    # 如果相似度已经超过阈值，可以提前停止
                    if similarity >= self.similarity_threshold:
                        break
                
                # 报告当前处理的文件
                if most_similar_file:
                    self.file_processed.emit(source_file, most_similar_file, max_similarity)
                
                # 如果相似度超过阈值，删除源文件
                if max_similarity >= self.similarity_threshold:
                    if not self.preview_mode:
                        self._delete_file(source_file)
                        self.file_deleted.emit(source_file)
                    deleted_count += 1
            
            self.processing_finished.emit(processed_count, deleted_count)
            
        except Exception as e:
            self.error_occurred.emit(f"处理过程中发生错误: {str(e)}")
        finally:
            self.is_processing = False
    
    def process_similarity_comparison_terminal(self, source_dir: str, target_dir: str):
        """处理图片相似度比较和删除（终端版本）"""
        import os
        
        print("=" * 60)
        print("图片相似度比较与删除工具")
        print("=" * 60)
        print(f"源目录: {source_dir}")
        print(f"目标目录: {target_dir}")
        print(f"相似度阈值: {self.similarity_threshold:.1%}")
        print(f"预览模式: {'是' if self.preview_mode else '否'}")
        print("-" * 60)
        
        try:
            # 获取文件列表
            source_files = self.get_image_files(source_dir)
            target_files = self.get_image_files(target_dir)
            
            if not source_files:
                print("❌ 源目录中没有找到图片文件")
                return
                
            if not target_files:
                print("❌ 目标目录中没有找到图片文件")
                return
            
            print(f"📁 源目录图片数量: {len(source_files)}")
            print(f"📁 目标目录图片数量: {len(target_files)}")
            print("-" * 60)
            
            # 创建目标文件的哈希缓存
            print("🔄 正在计算目标文件哈希值...")
            target_hashes = {}
            for i, target_file in enumerate(target_files):
                target_hashes[target_file] = self.calculate_image_hash(target_file)
                if (i + 1) % 20 == 0:
                    print(f"   已处理 {i + 1}/{len(target_files)} 个目标文件")
            
            deleted_count = 0
            processed_count = 0
            
            print("\n🔄 开始比较图片相似度...")
            for i, source_file in enumerate(source_files):
                processed_count += 1
                source_name = os.path.basename(source_file)
                
                # 显示进度
                progress = (processed_count / len(source_files)) * 100
                print(f"\n[{progress:5.1f}%] 处理: {source_name}")
                
                # 计算源文件的哈希值
                source_hash = self.calculate_image_hash(source_file)
                
                # 先检查是否有完全相同的文件
                found_duplicate = False
                for target_file, target_hash in target_hashes.items():
                    if source_hash and source_hash == target_hash:
                        target_name = os.path.basename(target_file)
                        print(f"   ✅ 发现完全相同文件: {target_name} (相似度: 100%)")
                        if not self.preview_mode:
                            try:
                                os.remove(source_file)
                                print(f"   🗑️  已删除: {source_name}")
                                deleted_count += 1
                            except Exception as e:
                                print(f"   ❌ 删除失败: {str(e)}")
                        else:
                            print(f"   📋 [预览模式] 将被删除: {source_name}")
                            deleted_count += 1
                        found_duplicate = True
                        break
                
                if found_duplicate:
                    continue
                
                # 如果没有完全相同的文件，进行相似度比较
                max_similarity = 0.0
                most_similar_file = None
                
                # 限制比较的文件数量以提高性能
                max_comparisons = min(50, len(target_files))
                comparison_count = 0
                
                for target_file in target_files:
                    if comparison_count >= max_comparisons:
                        break
                        
                    similarity = self.calculate_similarity(source_file, target_file)
                    comparison_count += 1
                    
                    if similarity > max_similarity:
                        max_similarity = similarity
                        most_similar_file = target_file
                    
                    # 如果相似度已经超过阈值，可以提前停止
                    if similarity >= self.similarity_threshold:
                        break
                
                # 报告当前处理的文件
                if most_similar_file:
                    target_name = os.path.basename(most_similar_file)
                    similarity_percent = max_similarity * 100
                    print(f"   📊 最相似文件: {target_name} (相似度: {similarity_percent:.1f}%)")
                
                # 如果相似度超过阈值，删除源文件
                if max_similarity >= self.similarity_threshold:
                    if not self.preview_mode:
                        try:
                            os.remove(source_file)
                            print(f"   🗑️  已删除: {source_name}")
                            deleted_count += 1
                        except Exception as e:
                            print(f"   ❌ 删除失败: {str(e)}")
                    else:
                        print(f"   📋 [预览模式] 将被删除: {source_name}")
                        deleted_count += 1
            
            print("\n" + "=" * 60)
            print("🎉 处理完成!")
            print(f"📊 处理文件数: {processed_count}")
            print(f"🗑️  删除文件数: {deleted_count}")
            
            if self.preview_mode:
                print("📋 注意: 这是预览模式，没有实际删除文件")
                print("   如需实际删除，请取消预览模式后重新运行")
            else:
                print("✅ 文件已实际删除")
            
            print("=" * 60)
            
        except Exception as e:
            print(f"\n❌ 处理过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def process_similarity_comparison_new_terminal(self, source_dir: str, target_dir: str):
        """在新终端窗口中处理图片相似度比较和删除"""
        import subprocess
        import sys
        import os
        
        # 获取当前Python解释器路径
        python_exe = sys.executable
        
        # 创建临时脚本文件
        script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片相似度比较处理脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, r"{os.getcwd()}")

from yolo_tool.core.image_similarity import ImageSimilarityProcessor

def main():
    processor = ImageSimilarityProcessor()
    processor.set_similarity_threshold({self.similarity_threshold})
    processor.preview_mode = {str(self.preview_mode)}
    
    print("=" * 60)
    print("图片相似度比较与删除工具")
    print("=" * 60)
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    print(f"相似度阈值: {{processor.similarity_threshold:.1%}}")
    print(f"预览模式: {{'是' if processor.preview_mode else '否'}}")
    print("-" * 60)
    
    try:
        # 获取文件列表
        source_files = processor.get_image_files(r"{source_dir}")
        target_files = processor.get_image_files(r"{target_dir}")
        
        if not source_files:
            print("❌ 源目录中没有找到图片文件")
            return
            
        if not target_files:
            print("❌ 目标目录中没有找到图片文件")
            return
        
        print(f"📁 源目录图片数量: {{len(source_files)}}")
        print(f"📁 目标目录图片数量: {{len(target_files)}}")
        print("-" * 60)
        
        # 创建目标文件的哈希缓存
        print("🔄 正在计算目标文件哈希值...")
        target_hashes = {{}}
        for i, target_file in enumerate(target_files):
            target_hashes[target_file] = processor.calculate_image_hash(target_file)
            if (i + 1) % 20 == 0:
                print(f"   已处理 {{i + 1}}/{{len(target_files)}} 个目标文件")
        
        deleted_count = 0
        processed_count = 0
        
        print("\\n🔄 开始比较图片相似度...")
        for i, source_file in enumerate(source_files):
            processed_count += 1
            source_name = os.path.basename(source_file)
            
            # 显示进度
            progress = (processed_count / len(source_files)) * 100
            print(f"\\n[{{progress:5.1f}}%] 处理: {{source_name}}")
            
            # 计算源文件的哈希值
            source_hash = processor.calculate_image_hash(source_file)
            
            # 先检查是否有完全相同的文件
            found_duplicate = False
            for target_file, target_hash in target_hashes.items():
                if source_hash and source_hash == target_hash:
                    target_name = os.path.basename(target_file)
                    print(f"   ✅ 发现完全相同文件: {{target_name}} (相似度: 100%)")
                    if not processor.preview_mode:
                        try:
                            os.remove(source_file)
                            print(f"   🗑️  已删除: {{source_name}}")
                            deleted_count += 1
                        except Exception as e:
                            print(f"   ❌ 删除失败: {{str(e)}}")
                    else:
                        print(f"   📋 [预览模式] 将被删除: {{source_name}}")
                        deleted_count += 1
                    found_duplicate = True
                    break
            
            if found_duplicate:
                continue
            
            # 如果没有完全相同的文件，进行相似度比较
            max_similarity = 0.0
            most_similar_file = None
            
            # 限制比较的文件数量以提高性能
            max_comparisons = min(50, len(target_files))
            comparison_count = 0
            
            for target_file in target_files:
                if comparison_count >= max_comparisons:
                    break
                    
                similarity = processor.calculate_similarity(source_file, target_file)
                comparison_count += 1
                
                if similarity > max_similarity:
                    max_similarity = similarity
                    most_similar_file = target_file
                
                # 如果相似度已经超过阈值，可以提前停止
                if similarity >= processor.similarity_threshold:
                    break
            
            # 报告当前处理的文件
            if most_similar_file:
                target_name = os.path.basename(most_similar_file)
                similarity_percent = max_similarity * 100
                print(f"   📊 最相似文件: {{target_name}} (相似度: {{similarity_percent:.1f}}%)")
            
            # 如果相似度超过阈值，删除源文件
            if max_similarity >= processor.similarity_threshold:
                if not processor.preview_mode:
                    try:
                        os.remove(source_file)
                        print(f"   🗑️  已删除: {{source_name}}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"   ❌ 删除失败: {{str(e)}}")
                else:
                    print(f"   📋 [预览模式] 将被删除: {{source_name}}")
                    deleted_count += 1
        
        print("\\n" + "=" * 60)
        print("🎉 处理完成!")
        print(f"📊 处理文件数: {{processed_count}}")
        print(f"🗑️  删除文件数: {{deleted_count}}")
        
        if processor.preview_mode:
            print("📋 注意: 这是预览模式，没有实际删除文件")
            print("   如需实际删除，请取消预览模式后重新运行")
        else:
            print("✅ 文件已实际删除")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"\\n❌ 处理过程中发生错误: {{str(e)}}")
        import traceback
        traceback.print_exc()
    
    # 等待用户按键后关闭
    input("\\n按回车键关闭此窗口...")

if __name__ == "__main__":
    main()
'''
        
        # 创建临时脚本文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(script_content)
            temp_script = f.name
        
        try:
            # 在Windows上启动新终端窗口
            if os.name == 'nt':  # Windows
                cmd = f'start cmd /k "{python_exe}" "{temp_script}"'
                subprocess.Popen(cmd, shell=True)
            else:  # Linux/Mac
                # 尝试不同的终端模拟器
                terminals = ['gnome-terminal', 'xterm', 'konsole', 'terminator']
                for terminal in terminals:
                    try:
                        cmd = f'{terminal} -e "{python_exe} {temp_script}"'
                        subprocess.Popen(cmd, shell=True)
                        break
                    except FileNotFoundError:
                        continue
                else:
                    # 如果都找不到，使用默认方式
                    subprocess.Popen([python_exe, temp_script])
                    
        except Exception as e:
            print(f"启动新终端失败: {str(e)}")
            # 如果启动新终端失败，回退到当前终端
            self.process_similarity_comparison_terminal(source_dir, target_dir)
    
    def _delete_file(self, file_path: str):
        """删除文件"""
        try:
            os.remove(file_path)
        except Exception as e:
            self.error_occurred.emit(f"删除文件失败 {file_path}: {str(e)}")
    
    def stop_processing(self):
        """停止处理"""
        self.is_processing = False 