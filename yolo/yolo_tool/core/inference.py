import sys
from PyQt6.QtCore import QThread, pyqtSignal
from ..core.bbox import BoundingBox

class InferenceThread(QThread):
    """在后台线程中运行YOLO模型推理"""
    inference_done = pyqtSignal(list) # 参数是 BoundingBox 列表
    inference_error = pyqtSignal(str)

    def __init__(self, model, image_path, class_names):
        super().__init__()
        self.model = model
        self.image_path = image_path
        self.class_names = class_names

    def run(self):
        try:
            from ultralytics import YOLO

            results = self.model(self.image_path, verbose=False) # verbose=False 减少控制台输出
            
            if not results:
                self.inference_done.emit([])
                return

            new_annotations = []
            result = results[0] # 处理第一张图的结果
            img_h, img_w = result.orig_shape

            for box in result.boxes:
                class_id = int(box.cls[0])
                # 从模型的名字列表中获取类名
                class_name = self.model.names[class_id] if class_id in self.model.names else f"未知_{class_id}"
                coords = box.xyxy[0].tolist()
                x1, y1, x2, y2 = coords

                bbox = BoundingBox(x1, y1, x2, y2, class_id, class_name)
                new_annotations.append(bbox)

            self.inference_done.emit(new_annotations)

        except ImportError:
            self.inference_error.emit("未能导入 'ultralytics' 库。\n请确保已安装：pip install ultralytics")
        except Exception as e:
            self.inference_error.emit(f"自动标注时发生错误: \n{str(e)}") 