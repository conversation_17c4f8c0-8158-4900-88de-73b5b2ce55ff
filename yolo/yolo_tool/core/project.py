import os
import yaml
from PyQt6.QtGui import QImage
from .bbox import BoundingBox

class ProjectManager:
    """项目管理器，按照YOLO标准结构管理项目"""
    
    def __init__(self, project_root):
        self.project_root = project_root
        self.images_dir = os.path.join(project_root, 'images') if project_root else None
        self.labels_dir = os.path.join(project_root, 'labels') if project_root else None
        self.config_file = os.path.join(project_root, 'data.yaml') if project_root else None
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 创建标准YOLO目录结构
        if project_root:
            self.setup_project_structure()
    
    def setup_project_structure(self):
        """创建YOLO标准目录结构（仅创建目录，不移动图片）"""
        os.makedirs(self.images_dir, exist_ok=True)
        os.makedirs(self.labels_dir, exist_ok=True)
        
        # 创建或更新data.yaml
        self.create_initial_config()
    
    def prepare_for_training(self):
        """准备训练：将已标注的图片复制到YOLO训练目录"""
        if not os.path.exists(self.project_root):
            return False
            
        # 获取所有已标注的图片
        image_files = self.get_image_files()
        annotated_files = []
        
        for image_path in image_files:
            if self.get_annotation_status(image_path):
                annotated_files.append(image_path)
        
        if not annotated_files:
            return False
            
        # 复制已标注的图片到images目录
        import shutil
        for image_path in annotated_files:
            try:
                filename = os.path.basename(image_path)
                dst_path = os.path.join(self.images_dir, filename)
                
                # 如果目标文件不存在，则复制
                if not os.path.exists(dst_path):
                    shutil.copy2(image_path, dst_path)
                    print(f"复制图片: {filename} -> images/")
                    
                # 确保对应的标注文件也在labels目录中
                label_name = os.path.splitext(filename)[0] + '.txt'
                src_label_path = os.path.join(self.labels_dir, label_name)
                if not os.path.exists(src_label_path):
                    # 如果labels目录中没有对应的标注文件，创建一个空文件
                    with open(src_label_path, 'w') as f:
                        pass
                        
            except Exception as e:
                print(f"复制图片失败 {os.path.basename(image_path)}: {e}")
        
        return True
    
    def create_initial_config(self):
        """创建初始的YOLO配置文件"""
        if not os.path.exists(self.config_file):
            config = {
                'train': self.images_dir,
                'val': self.images_dir,
                'names': []
            }
            
            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    yaml.safe_dump(config, f, allow_unicode=True)
                print("已创建 data.yaml 配置文件")
            except Exception as e:
                print(f"创建配置文件失败: {e}")
    
    def get_annotation_status(self, image_path):
        """获取图片的标注状态"""
        if not self.labels_dir or not os.path.exists(self.labels_dir):
            return False
            
        fname = os.path.splitext(os.path.basename(image_path))[0] + ".txt"
        ann_path = os.path.join(self.labels_dir, fname)
        
        return os.path.exists(ann_path) and os.path.getsize(ann_path) > 0
    
    def get_image_files(self):
        """获取项目根目录中的所有图片文件（递归搜索）"""
        if not self.project_root or not os.path.exists(self.project_root):
            return []
            
        image_files = []
        # 递归搜索项目根目录下的所有图片文件
        for root, dirs, files in os.walk(self.project_root):
            # 跳过YOLO训练目录
            if 'images' in dirs:
                dirs.remove('images')
            if 'labels' in dirs:
                dirs.remove('labels')
            
            for file in files:
                if os.path.splitext(file)[1].lower() in self.supported_formats:
                    image_files.append(os.path.join(root, file))
        
        return sorted(image_files)
    
    def get_project_statistics(self):
        """获取项目统计信息"""
        image_files = self.get_image_files()
        total_images = len(image_files)
        annotated_images = sum(1 for img in image_files if self.get_annotation_status(img))
        
        return {
            'total': total_images,
            'annotated': annotated_images,
            'unannotated': total_images - annotated_images
        }
    
    def load_project_config(self):
        """加载项目配置"""
        if not os.path.exists(self.config_file):
            return []
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                return config.get('names', [])
        except Exception:
            return []
    
    def save_project_config(self, class_names):
        """保存项目配置"""
        config = {
            'train': self.images_dir,
            'val': self.images_dir,
            'names': class_names
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config, f, allow_unicode=True)
            return True
        except Exception:
            return False
    
    def validate_yolo_files(self):
        """验证YOLO文件格式和完整性"""
        if not os.path.exists(self.project_root):
            return False, "项目根目录不存在"
        
        # 检查必要的目录
        if not os.path.exists(self.images_dir):
            return False, "images目录不存在"
        
        if not os.path.exists(self.labels_dir):
            return False, "labels目录不存在"
        
        # 检查配置文件
        if not os.path.exists(self.config_file):
            return False, "data.yaml配置文件不存在"
        
        # 检查是否有图片文件
        image_files = []
        for root, dirs, files in os.walk(self.images_dir):
            for file in files:
                if os.path.splitext(file)[1].lower() in self.supported_formats:
                    image_files.append(os.path.join(root, file))
        
        if not image_files:
            return False, "images目录中没有找到图片文件"
        
        # 检查标注文件
        label_files = []
        for root, dirs, files in os.walk(self.labels_dir):
            for file in files:
                if file.endswith('.txt'):
                    label_files.append(os.path.join(root, file))
        
        if not label_files:
            return False, "labels目录中没有找到标注文件"
        
        # 检查data.yaml配置
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                if not config:
                    return False, "data.yaml文件格式错误"
                
                if 'names' not in config:
                    return False, "data.yaml中缺少类别名称配置"
                
                if not config['names']:
                    return False, "data.yaml中类别名称为空"
                
                if 'train' not in config or 'val' not in config:
                    return False, "data.yaml中缺少训练和验证路径配置"
        
        except Exception as e:
            return False, f"读取data.yaml文件失败: {str(e)}"
        
        return True, "数据集验证通过"


class AnnotationManager:
    """标注管理器，处理YOLO格式的标注操作"""
    
    def __init__(self, project_manager):
        self.project_manager = project_manager
        self.labels_dir = project_manager.labels_dir
    
    def save_annotations(self, image_path, annotations):
        """保存标注到labels目录"""
        if not self.labels_dir:
            return False
            
        fname = os.path.splitext(os.path.basename(image_path))[0] + ".txt"
        ann_path = os.path.join(self.labels_dir, fname)
        
        try:
            img = QImage(image_path)
            w, h = img.width(), img.height()
            
            with open(ann_path, 'w') as f:
                for bbox in annotations:
                    f.write(bbox.to_yolo_format(w, h) + '\n')
            return True
        except Exception:
            return False
    
    def load_annotations(self, image_path, class_names):
        """从labels目录加载标注"""
        if not self.labels_dir:
            return []
            
        fname = os.path.splitext(os.path.basename(image_path))[0] + ".txt"
        ann_path = os.path.join(self.labels_dir, fname)
        
        annotations = []
        if os.path.exists(ann_path):
            try:
                img = QImage(image_path)
                w, h = img.width(), img.height()
                
                with open(ann_path, 'r') as f:
                    for line in f:
                        bbox = BoundingBox.from_yolo_format(line, w, h, class_names)
                        if bbox:
                            annotations.append(bbox)
            except Exception:
                pass
        
        return annotations 