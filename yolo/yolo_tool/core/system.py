import psutil

class SystemInfoDetector:
    """系统信息检测器，用于推荐训练参数"""
    
    @staticmethod
    def get_gpu_info():
        """获取GPU信息"""
        try:
            import torch
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                return {
                    'available': True,
                    'count': gpu_count,
                    'name': gpu_name,
                    'memory_gb': gpu_memory
                }
        except ImportError:
            pass
        return {'available': False, 'count': 0, 'name': 'N/A', 'memory_gb': 0}
    
    @staticmethod
    def get_cpu_info():
        """获取CPU信息"""
        return {
            'cores': psutil.cpu_count(logical=False),
            'logical_cores': psutil.cpu_count(logical=True),
            'frequency': psutil.cpu_freq().current if psutil.cpu_freq() else 0
        }
    
    @staticmethod
    def get_memory_info():
        """获取内存信息"""
        mem = psutil.virtual_memory()
        return {
            'total_gb': mem.total / 1024**3,
            'available_gb': mem.available / 1024**3,
            'percent_used': mem.percent
        }
    
    @staticmethod
    def recommend_training_params():
        """根据系统配置推荐训练参数"""
        gpu_info = SystemInfoDetector.get_gpu_info()
        cpu_info = SystemInfoDetector.get_cpu_info()
        mem_info = SystemInfoDetector.get_memory_info()
        
        # 推荐参数 - 针对720x1280分辨率优化，优先保证效果和稳定性
        params = {
            'epochs': 200, # 增加默认轮数以追求更好效果
            'batch_size': 1,  # 默认使用极小批次，防止显存不足
            'learning_rate': 0.001,
            'patience': 30,  # 增加耐心值，小批次需要更多时间收敛
            'workers': 2,  # 降低工作线程数减少内存占用
            'device': 'cpu',
            'imgsz': 1152,  # 使用1152以更好适配720x1280分辨率，同时符合32倍数要求
            'default_model': 'yolo11m.pt'  # 默认选择Medium模型
        }
        
        # 根据GPU情况调整 - 针对720x1280高分辨率图片优化，充分利用显存
        if gpu_info['available']:
            params['device'] = 'cuda'
            # 针对12G显存优化：更激进的批次大小以充分利用GPU
            if gpu_info['memory_gb'] >= 16:
                params['batch_size'] = 12   # 16GB+显存用大批次，充分利用显存
                params['imgsz'] = 1280       # 可以使用最高分辨率
                params['workers'] = 8        # 增加数据加载线程
            elif gpu_info['memory_gb'] >= 12:
                params['batch_size'] = 8    # 12GB显存用中等批次，目标80%显存利用率
                params['imgsz'] = 1152       # 使用1152更好适配720x1280
                params['workers'] = 6        # 增加数据加载线程减少GPU等待
            elif gpu_info['memory_gb'] >= 8:
                params['batch_size'] = 4    # 8GB显存用小批次
                params['imgsz'] = 1152       # 8GB显存也使用1152以获得更好效果
                params['workers'] = 4        # 适中的数据加载线程
            else:
                params['batch_size'] = 2    # 低显存用最小批次
                params['imgsz'] = 640        # 最低分辨率
                params['workers'] = 2        # 减少数据加载线程
        
        # 根据CPU情况调整workers
        import sys
        if sys.platform == 'win32':
            # Windows系统上减少worker数量，避免多进程问题
            if cpu_info['logical_cores'] >= 8:
                params['workers'] = 4
            elif cpu_info['logical_cores'] >= 4:
                params['workers'] = 2
            else:
                params['workers'] = 1
        else:
            # 其他系统使用正常配置
            if cpu_info['logical_cores'] >= 8:
                params['workers'] = 8
            elif cpu_info['logical_cores'] >= 4:
                params['workers'] = 4
            else:
                params['workers'] = 2
        
        # 根据内存情况调整
        if mem_info['total_gb'] < 8:
            params['batch_size'] = min(params['batch_size'], 8)
            params['workers'] = min(params['workers'], 2)
        
        return params
    
    @staticmethod
    def recommend_incremental_training_params(base_params=None):
        """推荐增量训练参数，基于基础参数进行优化"""
        if base_params is None:
            base_params = SystemInfoDetector.recommend_training_params()
        
        # 增量训练优化策略：更保守的参数，适合微调
        incremental_params = base_params.copy()
        
        # 学习率降低到1/10，适合在已有模型基础上微调
        incremental_params['learning_rate'] = base_params['learning_rate'] * 0.1
        
        # 训练轮数减少，增量训练通常不需要太多轮次
        if base_params['epochs'] > 50:
            incremental_params['epochs'] = max(30, base_params['epochs'] // 3)
        else:
            incremental_params['epochs'] = max(20, base_params['epochs'] // 2)
        
        # 增加早停耐心，给模型更多时间适应新数据
        incremental_params['patience'] = min(50, base_params['patience'] + 10)
        
        # 批次大小保持不变或略微减少，确保稳定性
        if base_params['batch_size'] > 1:
            incremental_params['batch_size'] = max(1, base_params['batch_size'] - 1)
        
        return incremental_params