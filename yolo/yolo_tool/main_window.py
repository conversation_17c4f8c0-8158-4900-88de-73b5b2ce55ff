import sys
import os
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QMessageBox, QFileDialog, QLabel, QListWidget, QListWidgetItem, QTreeWidget,
    QTreeWidgetItem, QSplitter, QComboBox, QDialog, QLineEdit, QDialogButtonBox,
    QStatusBar, QToolBar, QSizePolicy, QTreeWidgetItemIterator, QStyle, QGroupBox,
    QCheckBox
)
from PyQt6.QtGui import QColor, QBrush, QIcon, QAction, QFont, QKeySequence
from PyQt6.QtCore import Qt, QSize, QRect
from PyQt6.QtWidgets import QAbstractItemView

from .core.project import ProjectManager, AnnotationManager
from .core.bbox import BoundingBox
from .widgets.image_canvas import ImageCanvas
from .widgets.category_dialog import CategorySelectionDialog
from .widgets.training_dialog import TrainingDialog
from .widgets.similarity_dialog import SimilarityComparisonDialog
from .core.inference import InferenceThread

class YOLOAnnotationTool(QMainWindow):
    """YOLO标注工具主窗口 (重构版)"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("YOLO 标注与训练工具 - 扁平化版本")
        self.setGeometry(100, 100, 1600, 1000)  # 增大默认窗口大小
        self.setMinimumSize(1200, 800)  # 设置最小窗口大小
        
        # 使用更专业的颜色
        self.class_colors = [
            "#3498db", "#2ecc71", "#e74c3c", "#f1c40f", "#9b59b6",
            "#1abc9c", "#e67e22", "#34495e", "#7f8c8d", "#d35400"
        ]

        self.project_root = None
        self.image_files = []
        self.current_image_path = None
        self.current_index = -1
        self.class_names = []
        self.annotations: list[BoundingBox] = []
        self.last_used_classes = []
        
        self.project_manager = None
        self.annotation_manager = None
        self.inference_model = None # <<--- Changed from inference_thread

        self.setup_ui()
        self.setup_connections()
        self.update_ui_for_project_state()

    def setup_ui(self):
        # --- 主布局 ---
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)  # 去除主布局边距
        main_layout.setSpacing(0)  # 去除主布局间距

        # --- 工具栏 ---
        self.setup_toolbar()

        # --- 主内容区 ---
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(content_splitter, 1)

        # 左侧面板 (项目文件)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(8)
        
        filter_layout = QHBoxLayout()
        filter_layout.setSpacing(8)
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["全部", "已标注", "未标注"])
        self.filter_combo.currentTextChanged.connect(self.populate_file_tree)
        filter_layout.addWidget(QLabel("筛选:"))
        filter_layout.addWidget(self.filter_combo)
        
        left_layout.addLayout(filter_layout)
        self.file_tree = QTreeWidget()
        self.file_tree.setObjectName("file_tree")  # 设置对象名称以应用特殊样式
        self.file_tree.setHeaderLabel("项目文件")
        # 优化文件列表间距和性能
        self.file_tree.setIndentation(15)  # 减少缩进
        self.file_tree.setRootIsDecorated(True)
        self.file_tree.setAnimated(False)  # 禁用动画以提高性能
        self.file_tree.setUniformRowHeights(True)  # 统一行高以提高性能
        self.file_tree.setAlternatingRowColors(True)  # 启用交替行颜色
        left_layout.addWidget(self.file_tree)
        content_splitter.addWidget(left_panel)

        # 中间画布
        canvas_widget = QWidget()
        canvas_layout = QVBoxLayout(canvas_widget)
        canvas_layout.setContentsMargins(5, 5, 5, 5)
        canvas_layout.setSpacing(0)
        
        self.canvas = ImageCanvas(self)
        self.canvas.setStyleSheet("border: 1px solid #cccccc; background-color: #ffffff;")
        canvas_layout.addWidget(self.canvas)
        content_splitter.addWidget(canvas_widget)
        
        # 右侧面板 (类别与标注)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(8, 8, 8, 8)
        right_layout.setSpacing(12)

        # 类别管理
        class_group = QGroupBox("类别列表")
        class_layout = QVBoxLayout(class_group)
        class_layout.setSpacing(8)
        
        self.class_list = QListWidget()
        self.class_list.setObjectName("class_list")
        self.class_list.setAlternatingRowColors(True)  # 启用交替行颜色
        class_layout.addWidget(self.class_list)

        class_button_layout = QHBoxLayout()
        class_button_layout.setSpacing(8)
        self.add_class_btn = QPushButton("添加")
        self.add_class_btn.setToolTip("添加一个新类别")
        self.del_class_btn = QPushButton("删除")
        self.del_class_btn.setToolTip("删除选中的类别")
        class_button_layout.addWidget(self.add_class_btn)
        class_button_layout.addWidget(self.del_class_btn)
        class_layout.addLayout(class_button_layout)
        
        right_layout.addWidget(class_group)

        # 标注列表
        ann_group = QGroupBox("本图标注 (选中后可用键盘操作)")
        ann_layout = QVBoxLayout(ann_group)
        ann_layout.setSpacing(8)
        
        self.annotation_list = QListWidget()
        self.annotation_list.setObjectName("annotation_list")
        self.annotation_list.setAlternatingRowColors(True)  # 启用交替行颜色
        ann_layout.addWidget(self.annotation_list)

        ann_button_layout = QHBoxLayout()
        ann_button_layout.setSpacing(8)
        self.clear_ann_btn = QPushButton("清空标注")
        self.clear_ann_btn.setToolTip("清空当前图片的所有标注 (Alt+D)")
        self.clear_ann_btn.clicked.connect(self.clear_all_annotations)
        self.del_ann_btn = QPushButton("删除选中项")
        self.del_ann_btn.setToolTip("删除选中的标注 (快捷键: Delete)")
        ann_button_layout.addWidget(self.clear_ann_btn)
        ann_button_layout.addWidget(self.del_ann_btn)
        ann_layout.addLayout(ann_button_layout)
        
        right_layout.addWidget(ann_group)

        content_splitter.addWidget(right_panel)
        # 优化分割器比例：左侧文件列表(20%), 中间画布(60%), 右侧面板(20%)
        content_splitter.setSizes([280, 840, 280])
        content_splitter.setCollapsible(0, False)  # 左侧面板不可折叠
        content_splitter.setCollapsible(1, False)  # 中间画布不可折叠
        content_splitter.setCollapsible(2, False)  # 右侧面板不可折叠
        content_splitter.setChildrenCollapsible(False)  # 禁用所有子部件的折叠功能

        # --- 状态栏 ---
        self.setup_statusbar()

    def setup_toolbar(self):
        toolbar = QToolBar("主工具栏")
        toolbar.setIconSize(QSize(16, 16))
        self.addToolBar(toolbar)
        
        self.open_action = QAction("打开项目", self)
        self.open_action.triggered.connect(self.open_project_folder)
        toolbar.addAction(self.open_action)
        
        toolbar.addSeparator()
        
        self.prev_button = QPushButton(" 上一张")
        self.prev_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_ArrowBack))
        self.prev_button.clicked.connect(lambda: self.load_image_by_offset(-1))
        toolbar.addWidget(self.prev_button)
        
        self.next_button = QPushButton(" 下一张")
        self.next_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_ArrowForward))
        self.next_button.clicked.connect(lambda: self.load_image_by_offset(1))
        toolbar.addWidget(self.next_button)

        toolbar.addSeparator()

        self.load_model_button = QPushButton(" 加载模型")
        self.load_model_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DriveNetIcon))
        self.load_model_button.clicked.connect(self.load_inference_model)
        toolbar.addWidget(self.load_model_button)

        # 自动标注复选框和按钮
        self.auto_annotate_checkbox = QCheckBox("自动标注")
        self.auto_annotate_checkbox.stateChanged.connect(self.on_auto_annotate_checkbox_changed)
        toolbar.addWidget(self.auto_annotate_checkbox)
        self.auto_annotate_button = QPushButton(" 自动标注 (Alt+F)")
        self.auto_annotate_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_ComputerIcon))
        self.auto_annotate_button.clicked.connect(self.run_auto_annotation)
        toolbar.addWidget(self.auto_annotate_button)
        # 清理多余标注文件按钮
        self.clean_labels_button = QPushButton("清理多余标注文件")
        self.clean_labels_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_TrashIcon))
        self.clean_labels_button.clicked.connect(self.clean_orphan_labels)
        toolbar.addWidget(self.clean_labels_button)
        
        # 图片相似度比较按钮
        self.similarity_button = QPushButton("图片相似度比较")
        self.similarity_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogDetailedView))
        self.similarity_button.clicked.connect(self.open_similarity_dialog)
        toolbar.addWidget(self.similarity_button)
        
        widget = QWidget()
        spacer = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        widget.setSizePolicy(spacer)
        toolbar.addWidget(widget)

        self.train_button = QPushButton(" 训练")
        self.train_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_MediaPlay))
        self.train_button.clicked.connect(self.open_training_dialog)
        toolbar.addWidget(self.train_button)

        toolbar.addSeparator()
        help_action = QAction("帮助", self)
        help_action.triggered.connect(self.show_help_dialog)
        toolbar.addAction(help_action)


    def setup_statusbar(self):
        self.setStatusBar(QStatusBar())
        shortcuts = [
            "←/→: 切换图片",
            "↑/↓: (列表)选择",
            "Shift+←↑→↓: 缩放",
            "Delete: 删除框",
            "E: 编辑类别",
            "Ctrl+C/V: 复制/粘贴框",
            "Shift+C/V: 复制/粘贴全部"
        ]
        self.shortcut_label = QLabel(" | ".join(shortcuts))
        self.statusBar().addPermanentWidget(self.shortcut_label)


    def setup_connections(self):
        self.file_tree.itemClicked.connect(self.on_tree_item_clicked)
        self.annotation_list.itemClicked.connect(self.on_annotation_list_clicked)
        self.class_list.itemClicked.connect(self.on_class_list_clicked)
        self.class_list.itemDoubleClicked.connect(self.edit_class_name)

        self.add_class_btn.clicked.connect(self.add_class)
        self.del_class_btn.clicked.connect(self.delete_class)
        self.del_ann_btn.clicked.connect(self.delete_selected_annotation)
        self.clear_ann_btn.clicked.connect(self.clear_all_annotations)
        # 只展开一个文件夹
        self.file_tree.itemExpanded.connect(self.on_tree_item_expanded)

    def update_ui_for_project_state(self):
        is_project_open = self.project_root is not None
        self.train_button.setEnabled(is_project_open)
        self.load_model_button.setEnabled(is_project_open)
        self.auto_annotate_button.setEnabled(is_project_open and self.current_image_path is not None and self.inference_model is not None)
        self.prev_button.setEnabled(is_project_open and self.current_index > 0)
        self.next_button.setEnabled(is_project_open and self.current_index < len(self.image_files) - 1)
        
        if not is_project_open:
            self.file_tree.clear()
            self.class_list.clear()
            self.annotation_list.clear()
            self.canvas.set_image(None)

    def open_project_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择或创建YOLO项目文件夹")
        if folder:
            self.project_root = folder
            self.project_manager = ProjectManager(self.project_root)
            self.annotation_manager = AnnotationManager(self.project_manager)
            
            self.load_project_config()
            self.load_image_files()
            self.populate_file_tree()
            self.update_ui_for_project_state()
            self.statusBar().showMessage(f"项目已加载: {self.project_root}", 5000)

    def load_project_config(self):
        self.class_names = self.project_manager.load_project_config()
        self.update_class_ui()

    def save_project_config(self):
        if self.project_manager:
            self.project_manager.save_project_config(self.class_names)

    def load_image_files(self):
        self.image_files = self.project_manager.get_image_files()
        if self.image_files:
            self.load_image(0)
        else:
            self.current_index = -1
            self.current_image_path = None
            self.canvas.set_image(None)
            self.annotations = []
            self.update_annotation_list()

    def get_expanded_dirs(self):
        expanded = set()
        def recurse(item, rel_path):
            if item.childCount() > 0 and item.isExpanded():
                expanded.add(rel_path)
            for i in range(item.childCount()):
                child = item.child(i)
                if child.childCount() > 0:  # 只递归目录节点
                    child_name = child.text(0)
                    child_path = os.path.join(rel_path, child_name) if rel_path else child_name
                    recurse(child, child_path)
        root = self.file_tree.topLevelItem(0)
        if root:
            for i in range(root.childCount()):
                item = root.child(i)
                recurse(item, item.text(0))
        return expanded

    def restore_expanded_dirs(self, expanded_dirs):
        def recurse(item, rel_path):
            if rel_path in expanded_dirs:
                item.setExpanded(True)
            for i in range(item.childCount()):
                child = item.child(i)
                if child.childCount() > 0:  # 只递归目录节点
                    child_name = child.text(0)
                    child_path = os.path.join(rel_path, child_name) if rel_path else child_name
                    recurse(child, child_path)
        root = self.file_tree.topLevelItem(0)
        if root:
            for i in range(root.childCount()):
                item = root.child(i)
                recurse(item, item.text(0))

    def populate_file_tree(self):
        # --- 保持滚动条位置 ---
        vbar = self.file_tree.verticalScrollBar()
        scroll_pos = vbar.value() if vbar else 0
        # --- 保持展开状态 ---
        expanded_dirs = self.get_expanded_dirs()
        self.file_tree.clear()
        if not self.project_root: return

        root_item = QTreeWidgetItem(self.file_tree, [os.path.basename(self.project_root)])
        root_item.setData(0, Qt.ItemDataRole.UserRole, self.project_root)
        root_item.setExpanded(True)

        filter_text = self.filter_combo.currentText()
        
        dir_items = {'': root_item}
        image_files = self.project_manager.get_image_files()
        
        for image_path in image_files:
            is_annotated = self.project_manager.get_annotation_status(image_path)

            if filter_text == "已标注" and not is_annotated:
                continue
            if filter_text == "未标注" and is_annotated:
                continue
            
            # 创建目录层级
            parent_item = root_item
            rel_path = os.path.dirname(os.path.relpath(image_path, self.project_root))
            
            if rel_path and rel_path != '.':
                path_parts = rel_path.replace('\\', '/').split('/')
                current_path_key = ''
                for part in path_parts:
                    parent_path_key = current_path_key
                    current_path_key = os.path.join(current_path_key, part) if current_path_key else part
                    if current_path_key not in dir_items:
                        parent_node = dir_items.get(parent_path_key, root_item)
                        dir_items[current_path_key] = QTreeWidgetItem(parent_node, [part])
                        dir_items[current_path_key].setExpanded(False)
                parent_item = dir_items.get(rel_path, root_item)

            filename = os.path.basename(image_path)
            item = QTreeWidgetItem(parent_item, [filename])
            item.setData(0, Qt.ItemDataRole.UserRole, image_path)

            if is_annotated:
                item.setText(0, f"✓ {filename}")
                item.setForeground(0, QColor("#27ae60")) # Green
            else:
                item.setText(0, f"○ {filename}")
                item.setForeground(0, QColor("#f39c12")) # Orange
        
        stats = self.project_manager.get_project_statistics()
        root_item.setText(0, f"{os.path.basename(self.project_root)} ({stats['annotated']}/{stats['total']})")
        
        self.update_ui_for_project_state()
        # --- 恢复展开状态 ---
        self.restore_expanded_dirs(expanded_dirs)
        # --- 恢复滚动条位置 ---
        if vbar:
            vbar.setValue(scroll_pos)

    def on_tree_item_clicked(self, item, column):
        path = item.data(0, Qt.ItemDataRole.UserRole)
        if path and path in self.image_files:
            index = self.image_files.index(path)
            self.load_image(index)
    
    def load_image_by_offset(self, offset):
        if not self.image_files: return
        new_index = self.current_index + offset
        if 0 <= new_index < len(self.image_files):
            self.load_image(new_index)

    def load_image(self, index):
        if not (0 <= index < len(self.image_files)):
            return
        self.current_index = index
        self.current_image_path = self.image_files[index]
        self.canvas.set_image(self.current_image_path)
        self.load_annotations()
        self.update_annotation_list()
        # 自动标注逻辑：如果复选框勾选，且当前图片没有任何标注，则自动标注
        if hasattr(self, 'auto_annotate_checkbox') and self.auto_annotate_checkbox.isChecked():
            if not self.annotations:
                self.run_auto_annotation()
        self.update_ui_for_project_state()
        self.setWindowTitle(f"YOLO 工具 - {os.path.basename(self.current_image_path)}")
        self.select_file_tree_item_by_path(self.current_image_path)

    def add_annotation(self, rect: QRect):
        if not self.current_image_path or not rect.isValid():
            return
        if not self.class_names:
            QMessageBox.warning(self, "无法标注", "请先在右侧面板添加类别，然后再进行标注。")
            return
        dialog = CategorySelectionDialog(self, self.class_names, self.last_used_classes)
        if dialog.exec() != QDialog.DialogCode.Accepted:
            return
        selected_class_id = dialog.selected_class_id
        if selected_class_id < 0 or selected_class_id >= len(self.class_names):
            return
        selected_class_name = self.class_names[selected_class_id]
        
        img_w, img_h = self.canvas.current_pixmap.width(), self.canvas.current_pixmap.height()
        # 保持浮点数精度，避免强制转换为整数
        x1 = max(0.0, float(rect.left()))
        y1 = max(0.0, float(rect.top()))
        x2 = min(float(img_w), float(rect.right()))
        y2 = min(float(img_h), float(rect.bottom()))

        if (x2 - x1) < 5 or (y2 - y1) < 5: 
            return

        bbox = BoundingBox(x1, y1, x2, y2, selected_class_id, selected_class_name)
        self.annotations.append(bbox)
        
        # 更新最近使用类别列表，保持最新、去重、最多3个
        if selected_class_id in self.last_used_classes:
            self.last_used_classes.remove(selected_class_id)
        self.last_used_classes.insert(0, selected_class_id)
        self.last_used_classes = self.last_used_classes[:3]
        
        self.update_annotation_list()
        self.save_annotations()

    def select_annotation(self, index_to_select):
        """统一处理标注选择的逻辑，作为唯一入口"""
        # 如果索引无效，则取消所有选择
        if not (0 <= index_to_select < len(self.annotations)):
            for bbox in self.annotations:
                bbox.selected = False
        else:
            for i, bbox in enumerate(self.annotations):
                bbox.selected = (i == index_to_select)
        
        # 更新UI
        self.update_annotation_list() # 这会更新列表的视觉选择
        self.canvas.update()          # 这会更新画布上的高亮框
        self.canvas.setFocus()        # 将焦点设置到画布，解决键盘事件冲突

    def get_selected_annotation(self):
        """获取当前选中的标注框对象"""
        for i, bbox in enumerate(self.annotations):
            if bbox.selected:
                return i, bbox
        return -1, None

    def load_annotations(self):
        if not self.current_image_path or not self.annotation_manager:
            self.annotations = []
        else:
            self.annotations = self.annotation_manager.load_annotations(self.current_image_path, self.class_names)
        self.update_annotation_list()

    def save_annotations(self):
        if self.current_image_path and self.annotation_manager:
            self.annotation_manager.save_annotations(self.current_image_path, self.annotations)
            self.update_tree_item_status(self.current_image_path)
            stats = self.project_manager.get_project_statistics()
            root_item = self.file_tree.topLevelItem(0)
            if root_item:
                root_item.setText(0, f"{os.path.basename(self.project_root)} ({stats['annotated']}/{stats['total']})")
    
    def on_annotation_list_clicked(self, item):
        index = item.data(Qt.ItemDataRole.UserRole)
        self.select_annotation(index)
        
    def delete_selected_annotation(self):
        idx, bbox = self.get_selected_annotation()
        if not bbox:
            QMessageBox.information(self, "提示", "请先选择一个要删除的标注。")
            return
        
        reply = QMessageBox.question(self, "确认删除", f"确定要删除标注 '{bbox.class_name}' 吗?",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.No:
            return

        del self.annotations[idx]
        self.update_annotation_list()
        self.save_annotations()

    def clear_all_annotations(self):
        if not self.annotations: return
        reply = QMessageBox.question(self, "确认", "确定要清空当前图片的所有标注吗？ (Alt+D)",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.annotations.clear()
            self.update_annotation_list()
            self.save_annotations()
            self.select_annotation(-1)
            
    def delete_annotation_file(self):
        if not self.current_image_path:
            return

        fname = os.path.splitext(os.path.basename(self.current_image_path))[0] + ".txt"
        ann_path = os.path.join(self.project_manager.labels_dir, fname)
        img_path = self.current_image_path
        msg = f"确定要删除以下内容吗？ (Alt+Q)\n\n1. 标注文件: {fname}\n2. 图片文件: {os.path.basename(img_path)}\n\n此操作不可撤销。"
        reply = QMessageBox.question(self, "确认删除", msg,
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 删除标注文件
                if os.path.exists(ann_path):
                    os.remove(ann_path)
                # 删除图片文件
                if os.path.exists(img_path):
                    os.remove(img_path)
                self.statusBar().showMessage(f"已删除 {fname} 和图片", 3000)
                # 从图片列表中移除
                idx = self.current_index
                self.image_files.pop(idx)
                # 切换到下一张或上一张
                if self.image_files:
                    if idx >= len(self.image_files):
                        idx = len(self.image_files) - 1
                    self.load_image(idx)
                else:
                    self.current_index = -1
                    self.current_image_path = None
                    self.canvas.set_image(None)
                    self.annotations = []
                    self.update_annotation_list()
                self.populate_file_tree()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除文件失败: {e}")

    def update_annotation_list(self):
        self.annotation_list.clear()
        selected_index = -1
        for i, bbox in enumerate(self.annotations):
            item_text = f"ID {i+1}: {bbox.class_name}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, i)
            # Add a background color to distinguish items
            color = QColor(self.get_class_color(bbox.class_id))
            item.setBackground(QColor(color).lighter(180 if bbox.selected else 220))
            self.annotation_list.addItem(item)
            if bbox.selected:
                self.annotation_list.setCurrentItem(item)

    def on_class_list_clicked(self, item):
        # When a class is clicked, make it the current selection for the next annotation
        self.class_list.setCurrentItem(item)

        # If an annotation is also selected, change its class
        idx, bbox = self.get_selected_annotation()
        if not bbox: return
        
        class_index = self.class_list.row(item)
        
        if 0 <= idx < len(self.annotations):
            self.annotations[idx].class_id = class_index
            self.annotations[idx].class_name = self.class_names[class_index]
            self.update_annotation_list()
            self.save_annotations()

    def update_class_ui(self):
        current_selection = self.class_list.currentItem().text() if self.class_list.currentItem() else None
        self.class_list.clear()
        for name in self.class_names:
            item = QListWidgetItem(name)
            self.class_list.addItem(item)
            if name == current_selection:
                self.class_list.setCurrentItem(item)
    
    def add_class(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("添加新类别")
        layout = QVBoxLayout(dialog)
        entry = QLineEdit()
        layout.addWidget(QLabel("输入新类别名称:"))
        layout.addWidget(entry)
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        layout.addWidget(button_box)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)

        if dialog.exec():
            name = entry.text().strip()
            if name and name not in self.class_names:
                self.class_names.append(name)
                self.update_class_ui()
                self.save_project_config()

    def delete_class(self):
        selected_items = self.class_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "提示", "请先在列表中选择要删除的类别。")
            return
        
        class_name_to_del = selected_items[0].text()
        reply = QMessageBox.question(self, "确认删除", f"确定要删除类别 '{class_name_to_del}' 吗？\n所有使用此别的的标注将被移除！",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            class_id_to_del = self.class_names.index(class_name_to_del)
            self.class_names.pop(class_id_to_del)
            
            # This is complex: need to update annotations in all files
            # For now, just update UI and save config
            self.update_class_ui()
            self.save_project_config()
            QMessageBox.information(self, "提示", "类别已删除。这可能导致现有标注文件不一致，建议重新检查所有图片的标注。")


    def edit_class_name(self, item):
        old_name = item.text()
        if not old_name: return

        dialog = QDialog(self)
        dialog.setWindowTitle(f"编辑类别: {old_name}")
        layout = QVBoxLayout(dialog)
        entry = QLineEdit(old_name)
        layout.addWidget(QLabel("输入新名称:"))
        layout.addWidget(entry)
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        layout.addWidget(button_box)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        
        if dialog.exec():
            new_name = entry.text().strip()
            if new_name and new_name != old_name and new_name not in self.class_names:
                index = self.class_names.index(old_name)
                self.class_names[index] = new_name
                self.update_class_ui()
                self.save_project_config()
                # A full update of annotations is complex, warn the user for now
                QMessageBox.information(self, "提示", "类别名称已更新。请注意，这可能导致现有标注文件不一致，建议重新检查所有图片的标注。")


    def get_class_color(self, class_id: int) -> str:
        return self.class_colors[class_id % len(self.class_colors)]
    
    def update_tree_item_status(self, image_path):
        """Finds and updates the status icon/color of a single item in the file tree."""
        if not image_path: return
        
        iterator = QTreeWidgetItemIterator(self.file_tree)
        while iterator.value():
            item = iterator.value()
            if item.data(0, Qt.ItemDataRole.UserRole) == image_path:
                is_annotated = self.project_manager.get_annotation_status(image_path)
                filename = os.path.basename(image_path)
                if is_annotated:
                    item.setText(0, f"✓ {filename}")
                    item.setForeground(0, QColor("#27ae60")) # Green
                else:
                    item.setText(0, f"○ {filename}")
                    item.setForeground(0, QColor("#f39c12")) # Orange
                break
            iterator += 1

    def current_class_id(self):
        if not self.class_list.currentItem():
            return None
        return self.class_list.currentRow()

    def open_training_dialog(self):
        if not self.project_root:
            QMessageBox.warning(self, "错误", "请先创建或打开YOLO项目！")
            return
        dialog = TrainingDialog(self, self.project_root, self.project_manager)
        dialog.exec()

    def keyPressEvent(self, event):
        key = event.key()
        modifiers = event.modifiers()
        idx, selected_bbox = self.get_selected_annotation()

        # --- Priority 1: Actions requiring a selected box ---
        if selected_bbox:
            # Alt+E to Edit Class
            if modifiers == Qt.KeyboardModifier.AltModifier and key == Qt.Key.Key_E:
                self.edit_selected_annotation_class()
                event.accept()
                return
            
            # Delete key to delete box
            if modifiers == Qt.KeyboardModifier.NoModifier and key == Qt.Key.Key_Delete:
                self.delete_selected_annotation()
                event.accept()
                return

            # Ctrl+C/V for single box copy/paste
            if modifiers == Qt.KeyboardModifier.ControlModifier:
                if key == Qt.Key.Key_C: self.copy_selected_bbox(); event.accept(); return
                if key == Qt.Key.Key_V: self.paste_selected_bbox(); event.accept(); return
            
            # Arrow keys for move/resize
            move_step, resize_step = 1.0, 1.0
            is_move_resize_key = key in [Qt.Key.Key_Up, Qt.Key.Key_Down, Qt.Key.Key_Left, Qt.Key.Key_Right]

            if is_move_resize_key:
                if modifiers == Qt.KeyboardModifier.NoModifier:
                    if key == Qt.Key.Key_Up: selected_bbox.y1 -= move_step; selected_bbox.y2 -= move_step
                    elif key == Qt.Key.Key_Down: selected_bbox.y1 += move_step; selected_bbox.y2 += move_step
                    elif key == Qt.Key.Key_Left: selected_bbox.x1 -= move_step; selected_bbox.x2 -= move_step
                    elif key == Qt.Key.Key_Right: selected_bbox.x1 += move_step; selected_bbox.x2 += move_step
                elif modifiers == Qt.KeyboardModifier.ShiftModifier:
                    if key == Qt.Key.Key_Up: selected_bbox.y2 -= resize_step
                    elif key == Qt.Key.Key_Down: selected_bbox.y2 += resize_step
                    elif key == Qt.Key.Key_Left: selected_bbox.x2 -= resize_step
                    elif key == Qt.Key.Key_Right: selected_bbox.x2 += resize_step
                
                selected_bbox.normalize()
                self.canvas.update()
                self.update_annotation_list()
                self.save_annotations()
                event.accept(); return

        # --- Priority 2: Global actions ---
        # Alt shortcuts
        if modifiers == Qt.KeyboardModifier.AltModifier:
            if key == Qt.Key.Key_F: self.run_auto_annotation(); event.accept(); return
            if key == Qt.Key.Key_D: self.clear_all_annotations(); event.accept(); return
            if key == Qt.Key.Key_Q: self.delete_annotation_file(); event.accept(); return
            # Alt+F is handled by QAction's shortcut <- This comment is wrong.

        # Shift+C/V for all annotations copy/paste
        if modifiers == Qt.KeyboardModifier.ShiftModifier:
            if key == Qt.Key.Key_C: self.copy_all_annotations(); event.accept(); return
            if key == Qt.Key.Key_V: self.paste_all_annotations(); event.accept(); return

        # Left/Right for previous/next image
        if modifiers == Qt.KeyboardModifier.NoModifier:
            if key == Qt.Key.Key_Right:
                self.load_image_by_offset(1)
                event.accept()
                return
            if key == Qt.Key.Key_Left:
                self.load_image_by_offset(-1)
                event.accept()
                return
        
        # If nothing matched, pass it on
        super().keyPressEvent(event)

    def edit_selected_annotation_class(self):
        idx, bbox = self.get_selected_annotation()
        if not bbox:
            QMessageBox.information(self, "提示", "没有选中的标注框。")
            return
        
        dialog = CategorySelectionDialog(self, self.class_names, bbox.class_id)
        if dialog.exec() == QDialog.DialogCode.Accepted and dialog.selected_class_id != -1:
            bbox.class_id = dialog.selected_class_id
            bbox.class_name = self.class_names[bbox.class_id]
            # 更新最近使用类别列表，保持最新、去重、最多3个
            if bbox.class_id in self.last_used_classes:
                self.last_used_classes.remove(bbox.class_id)
            self.last_used_classes.insert(0, bbox.class_id)
            self.last_used_classes = self.last_used_classes[:3]
            self.update_annotation_list()
            self.save_annotations()

    def copy_selected_bbox(self):
        idx, bbox = self.get_selected_annotation()
        if bbox:
            # Simple deep copy
            self.copied_bbox = BoundingBox(bbox.x1, bbox.y1, bbox.x2, bbox.y2, bbox.class_id, bbox.class_name)
            self.statusBar().showMessage("已复制选中的标注框。", 2000)

    def paste_selected_bbox(self):
        if hasattr(self, 'copied_bbox') and self.copied_bbox:
            # Paste with a small offset
            self.copied_bbox.x1 += 10.0
            self.copied_bbox.y1 += 10.0
            self.copied_bbox.x2 += 10.0
            self.copied_bbox.y2 += 10.0
            # Ensure the copied bbox is a new instance
            new_bbox = BoundingBox(
                self.copied_bbox.x1, self.copied_bbox.y1, self.copied_bbox.x2, self.copied_bbox.y2,
                self.copied_bbox.class_id, self.copied_bbox.class_name
            )
            self.annotations.append(new_bbox)
            self.select_annotation(len(self.annotations) - 1)
            self.save_annotations()

    def copy_all_annotations(self):
        if not self.annotations:
            self.statusBar().showMessage("当前图片没有可复制的标注。", 2000)
            return
        self.copied_annotations = [
            BoundingBox(b.x1, b.y1, b.x2, b.y2, b.class_id, b.class_name) for b in self.annotations
        ]
        self.statusBar().showMessage(f"已复制全部 {len(self.copied_annotations)} 个标注。", 2000)

    def paste_all_annotations(self):
        if not hasattr(self, 'copied_annotations') or not self.copied_annotations:
            self.statusBar().showMessage("没有可粘贴的标注。", 2000)
            return
        
        reply = QMessageBox.question(self, "确认粘贴", 
                                     f"确定要粘贴 {len(self.copied_annotations)} 个标注到当前图片吗？\n（这会替换掉当前图片的所有标注）",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.annotations = self.copied_annotations
            self.select_annotation(-1) # Deselect all
            self.save_annotations()
            self.copied_annotations = [ # Refresh copied list
                 BoundingBox(b.x1, b.y1, b.x2, b.y2, b.class_id, b.class_name) for b in self.annotations
            ]

    def show_help_dialog(self):
        help_text = """
        <h3>环境准备与安装说明</h3>
        <h4>CUDA 安装（如需GPU加速，可选）</h4>
        <ol>
            <li>访问 <a href='https://developer.nvidia.com/cuda-downloads'>https://developer.nvidia.com/cuda-downloads</a></li>
            <li>Operating System 选 <b>Windows</b>，Architecture 选 <b>x86_64</b>，Version 选对应你的 <b>Win10/Win11</b>，Installer Type 选 <b>exe(local)</b></li>
            <li>安装时请选择 <b>自定义安装</b>，<span style='color:red'><b>只勾选CUDA，其他全部取消（重要！）</b></span></li>
            <li>如不需GPU加速，可跳过此步骤</li>
        </ol>
        <h4>PyTorch 安装</h4>
        <ol>
            <li>访问 <a href='https://pytorch.org/get-started/locally'>https://pytorch.org/get-started/locally</a></li>
            <li>PyTorch Build 选 <b>Stable</b>，Your OS 选 <b>Windows</b>，Package 选 <b>Pip</b>，Language 选 <b>Python</b></li>
            <li>Compute Platform 选最新的CUDA版本（如CUDA 11.8），如未安装CUDA或仅需CPU，选 <b>CPU</b></li>
            <li>复制页面下方生成的命令，在命令行中粘贴运行。例如：<br>
            <code>pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118</code><br>
            或CPU版本：<br>
            <code>pip3 install torch torchvision torchaudio</code></li>
        </ol>
        <p style='color:#888'>备注：安装CUDA时务必只勾选CUDA组件，PyTorch安装命令请以官网自动生成内容为准。</p>
        <hr/>
        <h3>YOLO标注工具快捷键</h3>
        <h4>全局操作</h4>
        <ul>
            <li><b>← / →</b>: 切换上一张 / 下一张图片</li>
            <li><b>Alt+F</b>: 使用模型自动标注当前图片</li>
            <li><b>Alt+D</b>: 清空当前图片的所有标注</li>
            <li><b>Alt+Q</b>: 删除当前图片的标注文件 (.txt)</li>
        </ul>
        <h4>标注框操作 (需先选中一个框)</h4>
        <ul>
            <li><b>← ↑ → ↓</b>: 移动标注框</li>
            <li><b>Shift + ← ↑ → ↓</b>: 调整标注框大小</li>
            <li><b>Delete</b>: 删除选中的标注框</li>
            <li><b>E</b>: 修改选中框的类别</li>
        </ul>
        <h4>复制与粘贴</h4>
        <ul>
            <li><b>Ctrl+C</b>: 复制选中的标注框</li>
            <li><b>Ctrl+V</b>: 粘贴已复制的标注框</li>
            <li><b>Shift+C</b>: 复制当前图片<b>所有</b>标注</li>
            <li><b>Shift+V</b>: <b>替换</b>当前图片为已复制的所有标注</li>
        </ul>
        """
        QMessageBox.information(self, "帮助", help_text)

    def load_inference_model(self):
        model_path, _ = QFileDialog.getOpenFileName(self, "选择YOLO模型文件", "", "PyTorch模型 (*.pt)")
        if not model_path:
            return
            
        self.statusBar().showMessage("正在加载模型...")
        QApplication.processEvents() # Force UI update

        try:
            from ultralytics import YOLO
            self.inference_model = YOLO(model_path)
            self.statusBar().showMessage(f"模型加载成功: {os.path.basename(model_path)}", 5000)
            self.update_ui_for_project_state()
        except Exception as e:
            QMessageBox.critical(self, "模型加载失败", f"加载模型时发生错误:\n{e}")
            self.statusBar().showMessage("模型加载失败")
            self.inference_model = None


    def run_auto_annotation(self):
        if not self.current_image_path:
            self.statusBar().showMessage("请先打开一张图片", 3000)
            return

        if not self.inference_model:
            self.statusBar().showMessage("请先加载一个模型", 3000)
            return

        self.statusBar().showMessage("正在进行自动标注...", 3000)
        self.auto_annotate_button.setEnabled(False)
        QApplication.processEvents()

        # 清空当前标注，为自动标注做准备
        self.annotations.clear()
        
        try:
            # predict()返回一个Results对象的列表, verbose=False可以关闭控制台输出
            results = self.inference_model.predict(self.current_image_path, verbose=False)
            result = results[0]  # 我们一次只处理一张图片

            # 从Results对象中提取标注框数据
            boxes_data = result.boxes
            
            if len(boxes_data) == 0:
                self.statusBar().showMessage("自动标注完成：未在本图中检测到任何目标。", 4000)
            else:
                # 获取tensor数据并移动到CPU
                xyxy_boxes = boxes_data.xyxy.cpu().numpy()
                labels_indices = boxes_data.cls.cpu().numpy().astype(int)
                class_name_map = result.names # 获取类别名称映射

                for i in range(len(xyxy_boxes)):
                    box = xyxy_boxes[i]
                    label_index = labels_indices[i]
                    category = class_name_map[label_index]
                    
                    x_min, y_min, x_max, y_max = box
                    
                    # 如果模型预测的类别不存在，则自动添加
                    if category not in self.class_names:
                        self.class_names.append(category)
                        self.update_class_ui()
                        self.save_project_config() # 自动保存新类别
                        
                    class_id = self.class_names.index(category)
                    bbox = BoundingBox(x_min, y_min, x_max, y_max, class_id, category)
                    self.annotations.append(bbox)
                
                self.statusBar().showMessage(f"自动标注完成，共检测到 {len(self.annotations)} 个目标", 4000)

            # 无论是否检测到，都更新UI并保存（保存空文件或新标注）
            self.update_annotation_list()
            self.save_annotations()
            self.canvas.update()  # 自动刷新画布，立即显示线框

        except Exception as e:
            self.statusBar().showMessage(f"自动标注失败: {e}", 5000)
            print(f"Error during auto-annotation: {e}")
        finally:
            self.auto_annotate_button.setEnabled(True)

    def select_file_tree_item_by_path(self, image_path):
        """在文件树中选中指定图片路径的节点"""
        if not image_path:
            return
        iterator = QTreeWidgetItemIterator(self.file_tree)
        while iterator.value():
            item = iterator.value()
            if item.data(0, Qt.ItemDataRole.UserRole) == image_path:
                self.file_tree.setCurrentItem(item)
                self.file_tree.scrollToItem(item)
                break
            iterator += 1

    def on_auto_annotate_checkbox_changed(self, state):
        if self.auto_annotate_checkbox.isChecked() and not self.inference_model:
            QMessageBox.information(self, "提示", "请先选择一个YOLO模型进行自动标注。")
            self.load_inference_model()
            if not self.inference_model:
                self.auto_annotate_checkbox.setChecked(False)

    def clean_orphan_labels(self):
        """删除 labels 目录下没有对应图片的 txt 文件"""
        if not self.project_manager or not hasattr(self.project_manager, 'labels_dir'):
            QMessageBox.warning(self, "错误", "未检测到项目或标注目录。")
            return
        labels_dir = self.project_manager.labels_dir
        if not os.path.exists(labels_dir):
            QMessageBox.information(self, "提示", "labels 目录不存在，无需清理。")
            return
        # 获取所有图片文件名（不含扩展名）
        image_basenames = set()
        for img_path in self.image_files:
            base = os.path.splitext(os.path.basename(img_path))[0]
            image_basenames.add(base)
        # 检查 labels 目录下所有 txt
        removed = 0
        for fname in os.listdir(labels_dir):
            if fname.lower().endswith('.txt'):
                base = os.path.splitext(fname)[0]
                if base not in image_basenames:
                    try:
                        os.remove(os.path.join(labels_dir, fname))
                        removed += 1
                    except Exception as e:
                        print(f"无法删除 {fname}: {e}")
        QMessageBox.information(self, "清理完成", f"已删除 {removed} 个多余的标注文件。")
    
    def open_similarity_dialog(self):
        """打开图片相似度比较对话框"""
        dialog = SimilarityComparisonDialog(self)
        dialog.exec()

    def on_tree_item_expanded(self, item):
        # 只展开一个文件夹，展开当前item时折叠其他同级文件夹
        parent = item.parent()
        if parent is None:
            parent = self.file_tree.invisibleRootItem()
        for i in range(parent.childCount()):
            sibling = parent.child(i)
            if sibling is not item and sibling.childCount() > 0 and sibling.isExpanded():
                sibling.setExpanded(False)