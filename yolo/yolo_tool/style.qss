/* 浅色扁平化主题 - YOLO标注工具 (高性能版) */

/* === 全局样式 === */
QWidget {
    background-color: #ffffff;
    color: #333333;
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    font-size: 13px;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
}

QMainWindow {
    background-color: #f5f5f5;
}

QDialog {
    background-color: #ffffff;
    border: 1px solid #cccccc;
}

/* === 组框样式 === */
QGroupBox {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 0px;
    margin-top: 10px;
    padding: 10px;
    font-weight: bold;
    color: #333333;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 8px;
    left: 8px;
    color: #0078d4;
    font-size: 14px;
    font-weight: bold;
    background-color: #ffffff;
}

/* === 按钮样式 === */
QPushButton {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-radius: 0px;
    padding: 8px 16px;
    font-size: 13px;
    color: #333333;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #e8e8e8;
    border-color: #0078d4;
}

QPushButton:pressed {
    background-color: #d0d0d0;
}

QPushButton:disabled {
    background-color: #f8f8f8;
    color: #999999;
    border-color: #e0e0e0;
}

/* 特殊按钮样式 */
QPushButton#StartTrainButton {
    background-color: #107c10;
    border-color: #107c10;
    color: white;
    font-weight: bold;
}

QPushButton#StartTrainButton:hover {
    background-color: #0e6e0e;
}

QPushButton#StartTrainButton:pressed {
    background-color: #0c5c0c;
}

QPushButton#StopTrainButton {
    background-color: #d13438;
    border-color: #d13438;
    color: white;
    font-weight: bold;
}

QPushButton#StopTrainButton:hover {
    background-color: #b92b2f;
}

QPushButton#StopTrainButton:pressed {
    background-color: #a12226;
}

/* === 输入控件样式 === */
QLineEdit, QSpinBox, QDoubleSpinBox {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 0px;
    padding: 6px 8px;
    color: #333333;
    font-size: 13px;
    min-height: 20px;
}

QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #0078d4;
    outline: none;
}

QLineEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled {
    background-color: #f8f8f8;
    color: #999999;
    border-color: #e0e0e0;
}

/* === SpinBox 上下箭头按钮样式 === */
QSpinBox::up-button, QDoubleSpinBox::up-button {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-left: none;
    border-top: none;
    border-radius: 0px;
    width: 16px;
    height: 12px;
    margin: 0px;
    padding: 0px;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
    background-color: #e8e8e8;
    border-color: #0078d4;
}

QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed {
    background-color: #d0d0d0;
}

QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
    width: 0px;
    height: 0px;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #333333;
    margin: 2px 4px;
}

QSpinBox::up-arrow:hover, QDoubleSpinBox::up-arrow:hover {
    border-bottom-color: #0078d4;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-left: none;
    border-top: none;
    border-radius: 0px;
    width: 16px;
    height: 12px;
    margin: 0px;
    padding: 0px;
}

QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #e8e8e8;
    border-color: #0078d4;
}

QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
    background-color: #d0d0d0;
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    width: 0px;
    height: 0px;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #333333;
    margin: 2px 4px;
}

QSpinBox::down-arrow:hover, QDoubleSpinBox::down-arrow:hover {
    border-top-color: #0078d4;
}

/* === 下拉框样式 === */
QComboBox {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 0px;
    padding: 6px 8px;
    color: #333333;
    font-size: 13px;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #0078d4;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #333333;
    margin-left: 4px;
}

QComboBox QAbstractItemView {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 0px;
    color: #333333;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
    outline: none;
}

/* === 列表和树形控件样式 === */
QListWidget, QTreeWidget {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 0px;
    color: #333333;
    outline: none;
    font-size: 13px;
    alternate-background-color: #f8f8f8;
}

QListWidget::item, QTreeWidget::item {
    padding: 3px 6px;
    margin: 0px;
    border-radius: 0px;
    min-height: 18px;
    border: none;
}

/* 文件树特殊样式 - 更紧凑的间距 */
QTreeWidget#file_tree::item {
    padding: 2px 4px;
    margin: 0px;
    min-height: 16px;
    border-radius: 0px;
}

QListWidget::item:hover, QTreeWidget::item:hover {
    background-color: #f0f0f0;
    color: #333333;
}

QListWidget::item:selected, QTreeWidget::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QListWidget::item:selected:!active, QTreeWidget::item:selected:!active {
    background-color: #cccccc;
    color: #333333;
}

/* === 文本编辑器样式 === */
QTextEdit {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 0px;
    color: #333333;
    font-family: "Consolas", "Courier New", monospace;
    font-size: 12px;
    padding: 8px;
}

QTextEdit:focus {
    border-color: #0078d4;
    outline: none;
}

/* === 标签页样式 === */
QTabWidget::pane {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 0px;
    margin-top: 0px;
}

QTabBar::tab {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-bottom: none;
    padding: 8px 16px;
    margin-right: 0px;
    border-radius: 0px;
    color: #333333;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    border-color: #cccccc;
    border-bottom: 1px solid #ffffff;
    color: #333333;
}

QTabBar::tab:hover:!selected {
    background-color: #e8e8e8;
}

/* === 工具栏样式 === */
QToolBar {
    background-color: #f0f0f0;
    border: none;
    border-bottom: 1px solid #cccccc;
    spacing: 4px;
    padding: 4px;
}

QToolBar::separator {
    background-color: #cccccc;
    width: 1px;
    margin: 4px;
}

/* === 状态栏样式 === */
QStatusBar {
    background-color: #f0f0f0;
    border-top: 1px solid #cccccc;
    color: #333333;
    padding: 4px;
}

/* === 滚动条样式 === */
QScrollBar:vertical {
    background-color: #f0f0f0;
    width: 16px;
    border-radius: 0px;
    margin: 0;
    border: 1px solid #cccccc;
}

QScrollBar::handle:vertical {
    background-color: #cccccc;
    border-radius: 0px;
    min-height: 20px;
    margin: 1px;
    border: 1px solid #aaaaaa;
}

QScrollBar::handle:vertical:hover {
    background-color: #bbbbbb;
}

QScrollBar::handle:vertical:pressed {
    background-color: #aaaaaa;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0;
}

QScrollBar:horizontal {
    background-color: #f0f0f0;
    height: 16px;
    border-radius: 0px;
    margin: 0;
    border: 1px solid #cccccc;
}

QScrollBar::handle:horizontal {
    background-color: #cccccc;
    border-radius: 0px;
    min-width: 20px;
    margin: 1px;
    border: 1px solid #aaaaaa;
}

QScrollBar::handle:horizontal:hover {
    background-color: #bbbbbb;
}

QScrollBar::handle:horizontal:pressed {
    background-color: #aaaaaa;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0;
}

/* === 进度条样式 === */
QProgressBar {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-radius: 0px;
    text-align: center;
    color: #333333;
    font-weight: bold;
}

QProgressBar::chunk {
    background-color: #0078d4;
    border-radius: 0px;
}

/* === 分割器样式 === */
QSplitter::handle {
    background-color: #cccccc;
    border: none;
}

QSplitter::handle:horizontal {
    width: 2px;
    margin: 0px;
    border-radius: 0px;
}

QSplitter::handle:vertical {
    height: 2px;
    margin: 0px;
    border-radius: 0px;
}

QSplitter::handle:hover {
    background-color: #0078d4;
}

/* === 工具提示样式 === */
QToolTip {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 0px;
    color: #333333;
    padding: 6px;
    font-size: 12px;
}

/* === 表单布局样式 === */
QFormLayout QLabel {
    color: #333333;
    font-weight: normal;
    padding-right: 8px;
}

/* === 特殊控件样式 === */
QHeaderView::section {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-left: none;
    padding: 6px;
    color: #333333;
    font-weight: bold;
}

QHeaderView::section:first {
    border-left: 1px solid #cccccc;
}

QHeaderView::section:hover {
    background-color: #e8e8e8;
}

/* === 对话框按钮样式 === */
QDialogButtonBox QPushButton {
    min-width: 80px;
    padding: 6px 12px;
}

/* === 菜单样式 === */
QMenuBar {
    background-color: #f0f0f0;
    border-bottom: 1px solid #cccccc;
    color: #333333;
}

QMenuBar::item {
    padding: 6px 12px;
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QMenu {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 0px;
    color: #333333;
    padding: 2px;
}

QMenu::item {
    padding: 6px 16px;
    border-radius: 0px;
}

QMenu::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QMenu::separator {
    height: 1px;
    background-color: #cccccc;
    margin: 2px 4px;
} 