from pypinyin import lazy_pinyin, Style
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QMessageBox, QGroupBox, 
    QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt

class CategorySelectionDialog(QDialog):
    """类别选择对话框"""
    def __init__(self, parent, class_names, last_used_classes=None):
        super().__init__(parent)
        self.setWindowTitle("选择标注类别")
        self.setModal(True)
        self.resize(600, 700)  # 增高弹窗，提升可视区域
        self.class_names = class_names
        self.last_used_classes = last_used_classes or []
        self.selected_class_id = -1
        self.setup_ui()
    
    def get_pinyin_key(self, text):
        """获取文本的拼音排序键"""
        if not text:
            return 'zzz'
        
        first_char = text[0]
        # 如果是数字或英文，直接返回
        if first_char.isascii():
            return first_char.lower()
        
        # 使用pypinyin获取拼音
        try:
            pinyin_list = lazy_pinyin(first_char, style=Style.NORMAL)
            if pinyin_list:
                return pinyin_list[0].lower()
        except:
            pass
        
        # 如果获取拼音失败，返回原字符
        return first_char
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 上次使用的类别（最多3个且不重复）
        if self.last_used_classes:
            last_group = QGroupBox("上次使用")
            last_layout = QVBoxLayout()
            shown = set()
            for class_id in self.last_used_classes:
                if 0 <= class_id < len(self.class_names) and class_id not in shown:
                    last_btn = QPushButton(f"[{class_id}] {self.class_names[class_id]}")
                    last_btn.setStyleSheet("QPushButton { text-align: left; padding: 6px; font-size: 12px; }")
                    last_btn.clicked.connect(lambda checked, cid=class_id: self.select_class(cid))
                    last_layout.addWidget(last_btn)
                    shown.add(class_id)
                    if len(shown) >= 3:
                        break
            last_group.setLayout(last_layout)
            layout.addWidget(last_group)
        
        # 所有类别区域
        all_group = QGroupBox("所有类别 (点击左侧字母索引快速定位，双击类别选择)")
        all_layout = QHBoxLayout()
        
        # 创建排序后的类别列表
        sorted_classes = []
        for i, class_name in enumerate(self.class_names):
            pinyin_key = self.get_pinyin_key(class_name)
            sorted_classes.append((pinyin_key, i, class_name))
        
        sorted_classes.sort(key=lambda x: x[0])
        
        # 左侧：字母索引列表
        index_widget = QListWidget()
        index_widget.setMaximumWidth(40)
        index_widget.setMinimumWidth(36)
        index_widget.setStyleSheet("QListWidget { font-size: 12px; padding: 2px; } QListWidget::item { min-height: 18px; }")
        
        # 右侧：类别列表
        self.class_list_widget = QListWidget()
        self.class_list_widget.setAlternatingRowColors(True)
        self.class_list_widget.setSpacing(2)  # 缩小行间距
        self.class_list_widget.setStyleSheet(
            "QListWidget { font-size: 12px; } "
            "QListWidget::item { padding: 2px 8px; min-height: 18px; }"
        )
        
        # 收集所有字母索引和对应的位置
        self.letter_positions = {}
        current_letter = None
        item_index = 0
        
        for pinyin_key, class_id, class_name in sorted_classes:
            first_letter = pinyin_key[0].upper()
            
            # 记录字母索引位置
            if first_letter != current_letter:
                current_letter = first_letter
                self.letter_positions[first_letter] = item_index
                
                # 添加字母索引到左侧列表（只添加一次）
                if not any(index_widget.item(i).text() == first_letter for i in range(index_widget.count())):
                    index_item = QListWidgetItem(first_letter)
                    index_item.setData(Qt.ItemDataRole.UserRole, first_letter)
                    index_widget.addItem(index_item)
            
            # 添加类别到右侧列表
            class_item = QListWidgetItem(f"[{class_id}] {class_name}")
            class_item.setData(Qt.ItemDataRole.UserRole, class_id)
            self.class_list_widget.addItem(class_item)
            item_index += 1
        
        # 连接索引列表点击事件
        index_widget.itemClicked.connect(self.on_index_clicked)
        self.index_widget = index_widget  # 保存引用，后续用
        
        # 连接类别列表点击事件
        self.class_list_widget.itemDoubleClicked.connect(self.on_class_item_clicked)
        
        # 添加到布局
        all_layout.addWidget(index_widget)
        all_layout.addWidget(self.class_list_widget)
        all_layout.setSpacing(8)
        
        all_group.setLayout(all_layout)
        layout.addWidget(all_group)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        select_btn = QPushButton("选择")
        select_btn.clicked.connect(self.on_select_clicked)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(select_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def on_index_clicked(self, item):
        """点击字母索引后，右侧只显示该字母开头的类别"""
        letter = item.text().upper()
        for i in range(self.class_list_widget.count()):
            class_item = self.class_list_widget.item(i)
            # 获取类别名的首字母
            text = class_item.text()
            # 兼容格式如 [0] 类别名
            if "]" in text:
                name = text.split("]", 1)[-1].strip()
            else:
                name = text.strip()
            first_letter = self.get_pinyin_key(name)[0].upper() if name else ''
            class_item.setHidden(first_letter != letter)
    
    def on_class_item_clicked(self, item):
        """双击类别项时选择该类别"""
        class_id = item.data(Qt.ItemDataRole.UserRole)
        self.select_class(class_id)
    
    def on_select_clicked(self):
        """点击选择按钮时选择当前选中的类别"""
        current_item = self.class_list_widget.currentItem()
        if current_item:
            class_id = current_item.data(Qt.ItemDataRole.UserRole)
            self.select_class(class_id)
        else:
            QMessageBox.warning(self, "提示", "请先选择一个类别")

    def select_class(self, class_id):
        """选择类别"""
        self.selected_class_id = class_id
        self.accept() 