from PyQt6.QtWidgets import QLabel, QMessageBox
from PyQt6.QtGui import QPixmap, QPainter, QPen, QColor, QFont, QFontMetrics
from PyQt6.QtCore import Qt, QPoint, QRect
from PyQt6.QtWidgets import QApplication

from ..core.bbox import BoundingBox
from .category_dialog import CategorySelectionDialog

class ImageCanvas(QLabel):
    """用于显示图像和绘制标注框的画布"""
    def __init__(self, parent):
        super().__init__(parent)
        self.main_window = parent
        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        self.drawing = False
        self.start_point = QPoint()
        self.end_point = QPoint()
        self.current_pixmap = None
        self.zoom_factor = 1.0
        self.panning = False
        self.pan_start_pos = QPoint()
        self.image_offset = QPoint()

    def set_image(self, image_path):
        if image_path:
            self.current_pixmap = QPixmap(image_path)
        else:
            self.current_pixmap = None
        self.zoom_factor = 1.0
        self.image_offset = QPoint()
        self.update()

    def mousePressEvent(self, event):
        if self.current_pixmap is None: return

        # 检查是否点击了已有的标注框
        # 从顶层开始检查，所以反向迭代
        for i in range(len(self.main_window.annotations) - 1, -1, -1):
            bbox = self.main_window.annotations[i]
            mapped_rect = self.map_from_original(bbox)
            label_rect = self._calculate_label_rect(mapped_rect, bbox.class_id, bbox.class_name)

            if mapped_rect.contains(event.pos()) or label_rect.contains(event.pos()):
                self.main_window.select_annotation(i)
                # 可以在这里增加拖动逻辑
                return

        if event.button() == Qt.MouseButton.LeftButton:
            if not self.main_window.class_names:
                 QMessageBox.information(self, "提示", "请先在右侧面板添加一个类别。")
                 return
            self.drawing = True
            self.start_point = event.pos()
            self.end_point = event.pos()
        elif event.button() == Qt.MouseButton.RightButton:
            self.panning = True
            self.pan_start_pos = event.pos()
            self.setCursor(Qt.CursorShape.ClosedHandCursor)

    def mouseMoveEvent(self, event):
        if self.drawing:
            self.end_point = event.pos()
            self.update()  # 实时重绘
        elif self.panning:
            self.image_offset += event.pos() - self.pan_start_pos
            self.pan_start_pos = event.pos()
            self.update()

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton and self.drawing:
            self.drawing = False
            rect = QRect(self.start_point, self.end_point).normalized()
            # 增加一个最小尺寸判断，避免误操作
            if rect.width() > 4 and rect.height() > 4:
                self.main_window.add_annotation(self.map_to_original(rect))
            self.update()
        elif event.button() == Qt.MouseButton.RightButton and self.panning:
            self.panning = False
            self.setCursor(Qt.CursorShape.ArrowCursor)

    def mouseDoubleClickEvent(self, event):
        if self.current_pixmap is None:
            return
        # 检查是否双击了某个标注的标签文字
        for i, bbox in enumerate(self.main_window.annotations):
            mapped_rect = self.map_from_original(bbox)
            label_rect = self._calculate_label_rect(mapped_rect, bbox.class_id, bbox.class_name)
            if label_rect.contains(event.pos()):
                # 弹出类别选择对话框
                dialog = CategorySelectionDialog(
                    self,
                    self.main_window.class_names,
                    self.main_window.last_used_classes
                )
                if dialog.exec() == dialog.DialogCode.Accepted:
                    new_class_id = dialog.selected_class_id
                    if 0 <= new_class_id < len(self.main_window.class_names):
                        bbox.class_id = new_class_id
                        bbox.class_name = self.main_window.class_names[new_class_id]
                        # 更新最近使用类别列表
                        if new_class_id in self.main_window.last_used_classes:
                            self.main_window.last_used_classes.remove(new_class_id)
                        self.main_window.last_used_classes.insert(0, new_class_id)
                        self.main_window.last_used_classes = self.main_window.last_used_classes[:3]
                        self.main_window.update_annotation_list()
                        self.main_window.save_annotations()
                        self.update()
                return

    def wheelEvent(self, event):
        if self.current_pixmap is None: return
        delta = event.angleDelta().y()
        
        # 获取鼠标在控件上的位置
        cursor_pos = event.position()

        # 计算缩放前的图像坐标
        old_x = (cursor_pos.x() - self.image_rect.x()) / self.image_rect.width()
        old_y = (cursor_pos.y() - self.image_rect.y()) / self.image_rect.height()

        # 计算缩放因子
        factor = 1.1 if delta > 0 else 1 / 1.1
        new_zoom = self.zoom_factor * factor
        
        # 限制缩放范围
        if new_zoom < 0.1 or new_zoom > 20:
             return
        self.zoom_factor = new_zoom
        
        # 强制重绘以更新image_rect
        self.update()
        QApplication.processEvents() # 处理挂起的事件

        # 计算缩放后的新图像坐标
        new_x = (cursor_pos.x() - self.image_rect.x()) / self.image_rect.width()
        new_y = (cursor_pos.y() - self.image_rect.y()) / self.image_rect.height()
        
        # 根据缩放中心的偏移来调整图像偏移量
        offset_x = self.image_offset.x() + (new_x - old_x) * self.image_rect.width()
        offset_y = self.image_offset.y() + (new_y - old_y) * self.image_rect.height()
        self.image_offset = QPoint(int(offset_x), int(offset_y))

        self.update()

    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QPainter(self)
        
        if self.current_pixmap is None: 
            painter.setPen(QColor(180, 180, 180))
            painter.setFont(QFont("Arial", 16))
            painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "请先创建或打开一个YOLO项目")
            return

        # 计算缩放后的尺寸
        scaled_pixmap = self.current_pixmap.scaled(
            self.size() * self.zoom_factor, 
            Qt.AspectRatioMode.KeepAspectRatio, 
            Qt.TransformationMode.SmoothTransformation
        )
        
        # 计算图像居中和拖拽偏移后的位置
        target_x = (self.width() - scaled_pixmap.width()) // 2 + self.image_offset.x()
        target_y = (self.height() - scaled_pixmap.height()) // 2 + self.image_offset.y()
        self.image_rect = QRect(target_x, target_y, scaled_pixmap.width(), scaled_pixmap.height())

        painter.drawPixmap(self.image_rect.topLeft(), scaled_pixmap)

        # 绘制已有的标注框
        for bbox in self.main_window.annotations:
            rect = self.map_from_original(bbox)
            class_color = self.main_window.get_class_color(bbox.class_id)
            
            pen_width = 3 if bbox.selected else 2
            color = QColor(class_color).lighter(150) if bbox.selected else QColor(class_color)
            
            painter.setPen(QPen(color, pen_width))
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawRect(rect)
            
            # 绘制标签
            self.draw_label(painter, rect, bbox.class_id, bbox.class_name, color)

        # 绘制正在创建的预览框
        if self.drawing:
            pen = QPen(QColor(255, 0, 0, 150), 2, Qt.PenStyle.DashLine)
            painter.setPen(pen)
            painter.setBrush(QColor(255, 0, 0, 20)) # 半透明填充
            painter.drawRect(QRect(self.start_point, self.end_point).normalized())
            
    def _calculate_label_rect(self, rect: QRect, class_id: int, class_name: str) -> QRect:
        """计算给定标注框的标签矩形位置"""
        font = QFont("Arial", 10, QFont.Weight.Bold)
        # 使用 QFontMetrics 是更安全、更推荐的计算方式
        metrics = QFontMetrics(font)
        text = f"{class_name} ({class_id})"
        text_width = metrics.horizontalAdvance(text) + 10
        text_height = metrics.height() + 4

        label_rect = QRect(rect.left(), rect.top() - text_height, text_width, text_height)
        
        if label_rect.top() < 0: label_rect.moveTop(rect.top())
        if label_rect.left() < 0: label_rect.moveLeft(rect.left())
            
        return label_rect

    def draw_label(self, painter, rect, class_id, class_name, color):
        """绘制标注标签"""
        label_rect = self._calculate_label_rect(rect, class_id, class_name)
        
        painter.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        painter.setBrush(color)
        painter.setPen(Qt.GlobalColor.white) # 文字颜色
        painter.drawRect(label_rect)
        painter.drawText(label_rect, Qt.AlignmentFlag.AlignCenter, f"{class_name} ({class_id})")

    def map_to_original(self, rect: QRect) -> QRect:
        """将画布坐标映射回原始图像坐标"""
        if self.current_pixmap is None or not self.image_rect.isValid(): return QRect()
        
        x_scale = self.current_pixmap.width() / self.image_rect.width()
        y_scale = self.current_pixmap.height() / self.image_rect.height()
        
        x1 = (rect.left() - self.image_rect.left()) * x_scale
        y1 = (rect.top() - self.image_rect.top()) * y_scale
        x2 = (rect.right() - self.image_rect.left()) * x_scale
        y2 = (rect.bottom() - self.image_rect.top()) * y_scale
        
        # 限制在图片范围内
        x1 = max(0, min(x1, self.current_pixmap.width()))
        y1 = max(0, min(y1, self.current_pixmap.height()))
        x2 = max(0, min(x2, self.current_pixmap.width()))
        y2 = max(0, min(y2, self.current_pixmap.height()))
        
        # 保持浮点数精度，只在最后创建QRect时转换为整数
        return QRect(round(x1), round(y1), round(x2-x1), round(y2-y1)).normalized()

    def map_from_original(self, bbox: BoundingBox) -> QRect:
        """将原始图像坐标映射到画布坐标"""
        if self.current_pixmap is None or not self.image_rect.isValid(): return QRect()

        x_scale = self.image_rect.width() / self.current_pixmap.width()
        y_scale = self.image_rect.height() / self.current_pixmap.height()

        # 保持浮点数精度，只在最后创建QRect时转换为整数
        x1 = bbox.x1 * x_scale + self.image_rect.left()
        y1 = bbox.y1 * y_scale + self.image_rect.top()
        x2 = bbox.x2 * x_scale + self.image_rect.left()
        y2 = bbox.y2 * y_scale + self.image_rect.top()
        
        return QRect(round(x1), round(y1), round(x2-x1), round(y2-y1)).normalized() 