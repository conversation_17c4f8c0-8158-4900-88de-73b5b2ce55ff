import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QLineEdit,
    QProgressBar, QTextEdit, QGroupBox, QSpinBox, QDoubleSpinBox,
    QMessageBox, QFileDialog, QCheckBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from ..core.image_similarity import ImageSimilarityProcessor

class SimilarityComparisonDialog(QDialog):
    """图片相似度比较对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("图片相似度比较与删除")
        self.setModal(True)
        self.setMinimumSize(600, 500)
        
        # 初始化处理器
        self.processor = ImageSimilarityProcessor()
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("图片相似度比较与删除工具")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明文字
        info_label = QLabel("⚠️ 注意：源目录中的相似图片将被删除，目标目录中的图片不会被删除")
        info_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        layout.addWidget(info_label)
        
        # 目录选择组
        dir_group = QGroupBox("目录选择")
        dir_layout = QVBoxLayout(dir_group)
        
        # 源目录
        source_layout = QHBoxLayout()
        source_layout.addWidget(QLabel("源目录:"))
        self.source_dir_edit = QLineEdit()
        self.source_dir_edit.setPlaceholderText("选择包含要检查的图片的目录")
        source_layout.addWidget(self.source_dir_edit)
        self.source_dir_btn = QPushButton("浏览")
        source_layout.addWidget(self.source_dir_btn)
        dir_layout.addLayout(source_layout)
        
        # 目标目录
        target_layout = QHBoxLayout()
        target_layout.addWidget(QLabel("目标目录:"))
        self.target_dir_edit = QLineEdit()
        self.target_dir_edit.setPlaceholderText("选择作为参考的图片目录")
        target_layout.addWidget(self.target_dir_edit)
        self.target_dir_btn = QPushButton("浏览")
        target_layout.addWidget(self.target_dir_btn)
        dir_layout.addLayout(target_layout)
        
        layout.addWidget(dir_group)
        
        # 参数设置组
        param_group = QGroupBox("参数设置")
        param_layout = QVBoxLayout(param_group)
        
        # 相似度阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("相似度阈值:"))
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.1, 1.0)
        self.threshold_spin.setValue(0.9)
        self.threshold_spin.setSingleStep(0.05)
        self.threshold_spin.setSuffix(" (90%)")
        threshold_layout.addWidget(self.threshold_spin)
        threshold_layout.addStretch()
        param_layout.addLayout(threshold_layout)
        
        # 预览选项
        self.preview_checkbox = QCheckBox("预览模式 (不实际删除文件)")
        self.preview_checkbox.setToolTip("勾选后只显示相似度，不会删除文件")
        param_layout.addWidget(self.preview_checkbox)
        
        layout.addWidget(param_group)
        
        # 说明组
        info_group = QGroupBox("处理说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QLabel(
            "点击'开始比较'后，程序将打开新的终端窗口显示处理进度。\n"
            "处理完成后新终端窗口会等待您按回车键后关闭。"
        )
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始比较")
        self.start_btn.setEnabled(False)
        button_layout.addWidget(self.start_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
    def setup_connections(self):
        # 目录选择按钮
        self.source_dir_btn.clicked.connect(self.select_source_directory)
        self.target_dir_btn.clicked.connect(self.select_target_directory)
        
        # 开始按钮
        self.start_btn.clicked.connect(self.start_comparison)
        self.close_btn.clicked.connect(self.close)
        
        # 处理器信号（简化版）
        self.processor.error_occurred.connect(self.on_error_occurred)
        
        # 阈值变化
        self.threshold_spin.valueChanged.connect(self.on_threshold_changed)
        
    def select_source_directory(self):
        """选择源目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择源目录", "",
            QFileDialog.Option.ShowDirsOnly
        )
        if directory:
            self.source_dir_edit.setText(directory)
            self.check_ready_state()
            
    def select_target_directory(self):
        """选择目标目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择目标目录", "",
            QFileDialog.Option.ShowDirsOnly
        )
        if directory:
            self.target_dir_edit.setText(directory)
            self.check_ready_state()
            
    def check_ready_state(self):
        """检查是否可以开始处理"""
        source_dir = self.source_dir_edit.text().strip()
        target_dir = self.target_dir_edit.text().strip()
        
        # 确保返回布尔值
        can_start = bool(source_dir and target_dir and 
                        os.path.exists(source_dir) and 
                        os.path.exists(target_dir) and
                        source_dir != target_dir)
        
        self.start_btn.setEnabled(can_start)
        
    def on_threshold_changed(self, value):
        """相似度阈值改变"""
        self.processor.set_similarity_threshold(value)
        
    def start_comparison(self):
        """开始比较"""
        source_dir = self.source_dir_edit.text().strip()
        target_dir = self.target_dir_edit.text().strip()
        
        # 确认对话框
        if not self.preview_checkbox.isChecked():
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除源目录中相似度大于{self.threshold_spin.value():.1%}的图片吗？\n"
                f"源目录: {source_dir}\n"
                f"目标目录: {target_dir}\n\n"
                "此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
        
        # 设置预览模式
        if self.preview_checkbox.isChecked():
            self.processor.preview_mode = True
        else:
            self.processor.preview_mode = False
        
        # 隐藏对话框
        self.hide()
        
        # 在新终端窗口中运行处理
        self.processor.process_similarity_comparison_new_terminal(source_dir, target_dir)
        
        # 处理完成后关闭对话框
        self.close()
        
    def stop_comparison(self):
        """停止比较（简化版）"""
        # 由于现在使用终端输出，停止功能已简化
        pass
            
    def on_error_occurred(self, error_message):
        """发生错误"""
        self.log_text.append(f"错误: {error_message}")
        QMessageBox.warning(self, "错误", error_message)
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.processing_thread and self.processing_thread.isRunning():
            reply = QMessageBox.question(
                self, "确认关闭",
                "正在处理中，确定要关闭吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.stop_comparison()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept() 