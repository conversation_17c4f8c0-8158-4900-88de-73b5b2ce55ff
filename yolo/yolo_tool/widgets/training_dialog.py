import sys
import os
import subprocess
import yaml
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QMessageBox, QGroupBox, QSpinBox, 
    QDoubleSpinBox, QTextEdit, QProgressBar, QTabWidget, QFormLayout, QComboBox, 
    QLabel, QWidget, QDialogButtonBox, QScrollArea, QCheckBox
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QUrl
from PyQt6.QtGui import QDesktopServices


from ..core.system import SystemInfoDetector

class TrainingThread(QThread):
    """训练线程"""
    progress_updated = pyqtSignal(str)
    # 信号更新：(成功状态, 消息, best.pt模型路径)
    training_finished = pyqtSignal(bool, str, str) 
    
    def __init__(self, project_root, params):
        super().__init__()
        self.project_root = project_root
        self.params = params
        self.process = None
        self.is_running = True
    
    def run(self):
        """运行训练"""
        best_model_path = ""
        try:
            # 创建训练脚本
            train_script = self.create_training_script()
            
            # 确定python解释器路径
            python_executable = sys.executable
            
            # 运行训练
            self.process = subprocess.Popen(
                [python_executable, train_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='replace',
                cwd=self.project_root,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0
            )
            
            while self.is_running and self.process.poll() is None:
                line = self.process.stdout.readline()
                if line:
                    self.progress_updated.emit(line.strip())
            
            # 确保进程已终止
            if self.process.poll() is None:
                self.process.terminate()
                self.process.wait()

            if self.is_running: # 如果不是被手动停止的
                if self.process.returncode == 0:
                    # 训练成功，查找 best.pt
                    results_dir = os.path.join(self.project_root, 'runs', 'train')
                    for root, _, files in os.walk(results_dir):
                        if 'best.pt' in files:
                            best_model_path = os.path.join(root, 'best.pt')
                            break
                    self.training_finished.emit(True, "训练成功完成！", best_model_path)
                else:
                    self.training_finished.emit(False, f"训练失败，退出代码: {self.process.returncode}", "")
            else:
                self.progress_updated.emit("训练已被用户手动停止。")
                self.training_finished.emit(False, "训练被手动停止", "")

        except Exception as e:
            self.training_finished.emit(False, f"训练启动时发生严重错误: {str(e)}", "")
    
    def create_training_script(self):
        """创建训练脚本"""
        # 将反斜杠替换为正斜杠，以兼容所有平台
        project_root_safe = self.project_root.replace('\\', '/')
        
        script_content = f"""
import os
import yaml
from ultralytics import YOLO
from multiprocessing import freeze_support

# 设置CUDA内存管理，减少显存碎片化
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

def main():
    try:
        # 项目路径
        project_root = r'{project_root_safe}'
        data_yaml = os.path.join(project_root, 'data.yaml')

        print("=" * 50)
        print(f"YOLO 项目验证")
        print(f"  - 项目根目录: {{project_root}}")
        print(f"  - 配置文件: {{data_yaml}}")
        print("=" * 50)

        # 创建模型
        if {str(self.params.get('incremental', False))}:
            # 增量训练模式：使用已训练的模型
            base_model_path = r'{self.params.get('base_model_path', '')}'
            if base_model_path and os.path.exists(base_model_path):
                print(f"正在加载已训练模型进行增量训练: {{base_model_path}}")
                model = YOLO(base_model_path)
                print("✅ 增量训练模式：基于已有模型继续训练")
            else:
                print("⚠️  未找到已训练模型，切换为普通训练模式")
                model = YOLO('{self.params['model_weight']}')
        else:
            # 普通训练模式：使用预训练模型
            print(f"正在加载 YOLOv11 预训练模型: {self.params['model_weight']}")
            model = YOLO('{self.params['model_weight']}')

        # 开始训练
        if {str(self.params.get('incremental', False))}:
            print("🔄 开始增量训练，基于已有模型继续优化...")
        else:
            print("🚀 开始完整训练，请稍候...")
            
        results = model.train(
            data=data_yaml,
            epochs={self.params['epochs']},
            batch={self.params['batch_size']},
            lr0={self.params['learning_rate']},
            patience={self.params['patience']},
            workers={self.params['workers']},
            device='{self.params['device']}',
            imgsz={self.params['imgsz']},  # 设置图片尺寸，针对720x1280优化
            project=project_root,
            name='runs/train', # 将结果保存到固定的 train 目录
            exist_ok=True, # 覆盖上一次的训练结果
            resume={str(self.params.get('incremental', False))}  # 增量训练时启用resume
        )

        print("训练过程正常结束！")

    except Exception as e:
        print(f"训练脚本执行失败: {{e}}")
        import traceback
        traceback.print_exc()
        exit(1)

if __name__ == '__main__':
    freeze_support()  # Windows多进程支持
    main()
"""
        
        script_path = os.path.join(self.project_root, 'train_yolo.py')
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_path
    
    def stop(self):
        """停止训练"""
        self.is_running = False
        if self.process and self.process.poll() is None:
            self.process.terminate()


class TrainingDialog(QDialog):
    """YOLO训练参数配置对话框"""
    
    def __init__(self, parent, project_root, project_manager):
        super().__init__(parent)
        self.project_root = project_root
        self.project_manager = project_manager
        self.training_thread = None
        self.setWindowTitle("YOLO 模型训练")
        self.setModal(True)
        self.resize(800, 600)  # 更宽更适中
        self.setMinimumSize(600, 400)  # 设置最小尺寸，防止溢出
        
        # 获取系统信息和推荐参数
        self.system_info = {
            'gpu': SystemInfoDetector.get_gpu_info(),
            'cpu': SystemInfoDetector.get_cpu_info(),
            'memory': SystemInfoDetector.get_memory_info()
        }
        self.recommended_params = SystemInfoDetector.recommend_training_params()
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        
        self.tab_widget = QTabWidget()
        # 训练参数Tab用滚动区包裹
        params_scroll = QScrollArea()
        params_scroll.setWidgetResizable(True)
        params_widget = self.create_params_tab()
        params_scroll.setWidget(params_widget)
        self.tab_widget.addTab(params_scroll, "训练参数")
        # 移除训练输出Tab
        self.tab_widget.addTab(self.create_system_info_tab(), "系统信息")
        self.tab_widget.addTab(self.create_help_tab(), "帮助说明")
        
        main_layout.addWidget(self.tab_widget)
        
        # 按钮区
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始训练")
        self.start_btn.setObjectName("StartTrainButton")
        self.start_btn.clicked.connect(self.start_training)
        
        self.stop_btn = QPushButton("停止训练")
        self.stop_btn.setObjectName("StopTrainButton")
        self.stop_btn.clicked.connect(self.stop_training)
        self.stop_btn.setEnabled(False)
        
        button_layout.addStretch()
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        main_layout.addLayout(button_layout)

    def create_system_info_tab(self):
        widget = QWidget()
        layout = QFormLayout(widget)
        layout.setRowWrapPolicy(QFormLayout.RowWrapPolicy.WrapAllRows)
        gpu_info = self.system_info['gpu']
        cpu_info = self.system_info['cpu']
        mem_info = self.system_info['memory']

        layout.addRow("GPU 可用:", QLabel("是" if gpu_info['available'] else "否"))
        if gpu_info['available']:
            layout.addRow("GPU 名称:", QLabel(gpu_info['name']))
            layout.addRow("GPU 显存:", QLabel(f"{gpu_info['memory_gb']:.1f} GB"))
        layout.addRow("CPU 物理核心:", QLabel(str(cpu_info['cores'])))
        layout.addRow("CPU 逻辑核心:", QLabel(str(cpu_info['logical_cores'])))
        layout.addRow("总内存:", QLabel(f"{mem_info['total_gb']:.1f} GB"))
        layout.addRow("可用内存:", QLabel(f"{mem_info['available_gb']:.1f} GB"))
        return widget
    
    def create_params_tab(self):
        widget = QWidget()
        main_layout = QVBoxLayout(widget)
        main_layout.setSpacing(15)

        # --- 新增模型选择 ---
        model_group = QGroupBox("YOLOv11 模型选择")
        model_layout = QVBoxLayout(model_group)
        self.model_combo = QComboBox()
        self.model_combo.addItems([
            "yolo11n.pt (Nano) - 极致轻量，适合边缘设备和实时场景，速度最快，精度较低",
            "yolo11s.pt (Small) - 轻量级，适合对速度和精度有一定要求的场景",
            "yolo11m.pt (Medium) - 平衡型，精度和速度兼顾，适合大多数应用",
            "yolo11l.pt (Large) - 高精度，适合对检测精度要求较高的场景",
            "yolo11x.pt (X-Large) - 最高精度，适合服务器端和高性能GPU，速度最慢"
        ])
        # 默认选择Medium模型
        if 'default_model' in self.recommended_params:
            default_model = self.recommended_params['default_model']
            for i in range(self.model_combo.count()):
                if self.model_combo.itemText(i).startswith(default_model):
                    self.model_combo.setCurrentIndex(i)
                    break
        model_layout.addWidget(self.model_combo)
        # 说明文字
        model_desc = QLabel(
            "<b>模型说明：</b><br>"
            "Nano (n)：最快速，适合移动端/嵌入式，精度最低<br>"
            "Small (s)：轻量级，速度快，精度较好<br>"
            "Medium (m)：速度与精度平衡，推荐大多数场景<br>"
            "Large (l)：高精度，适合高要求场景<br>"
            "X-Large (x)：最高精度，适合服务器/高端GPU，速度最慢"
        )
        model_desc.setStyleSheet("color: #666; font-size: 11px;")
        model_desc.setWordWrap(True)
        model_layout.addWidget(model_desc)
        main_layout.addWidget(model_group)

        # --- 核心参数组 ---
        basic_group = QGroupBox("⚙️ 核心参数")
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # 增量训练复选框
        incremental_layout = QHBoxLayout()
        self.incremental_checkbox = QCheckBox("启用增量训练")
        self.incremental_checkbox.setToolTip("基于已有模型继续训练，适合新增少量数据的场景")
        # 检查是否有已训练的模型
        self.has_trained_model = self.check_trained_models()
        self.incremental_checkbox.setEnabled(self.has_trained_model)
        if self.has_trained_model:
            self.incremental_checkbox.setChecked(False)  # 默认不勾选，让用户主动选择
        incremental_layout.addWidget(self.incremental_checkbox)
        incremental_help = QLabel("🔄 基于已有模型继续训练，适合数据集变化不大的增量更新场景。")
        if not self.has_trained_model:
            incremental_help.setText("❌ 项目目录下未找到已训练的模型文件，请先进行完整训练。")
            incremental_help.setStyleSheet("color: #999; font-size: 11px;")
        else:
            incremental_help.setStyleSheet("color: #666; font-size: 11px;")
        incremental_help.setWordWrap(True)
        incremental_layout.addWidget(incremental_help)
        basic_layout.addRow("增量训练:", incremental_layout)
        
        # 连接增量训练复选框的状态变化事件
        self.incremental_checkbox.stateChanged.connect(self.on_incremental_changed)

        # 训练轮数
        epochs_layout = QHBoxLayout()
        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(1, 1000)
        self.epochs_spin.setValue(self.recommended_params['epochs'])
        epochs_layout.addWidget(self.epochs_spin)
        epochs_help = QLabel("📊 模型遍历整个数据集的次数。轮数越多学习越充分，但可能过拟合。")
        epochs_help.setWordWrap(True)
        epochs_help.setStyleSheet("color: #666; font-size: 11px;")
        epochs_layout.addWidget(epochs_help)
        basic_layout.addRow("训练轮数 (Epochs):", epochs_layout)

        # 批次大小
        batch_layout = QHBoxLayout()
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 128)
        self.batch_size_spin.setValue(self.recommended_params['batch_size'])
        batch_layout.addWidget(self.batch_size_spin)
        batch_help = QLabel("📦 每次训练同时处理的图片数量。影响显存占用和训练稳定性。")
        batch_help.setWordWrap(True)
        batch_help.setStyleSheet("color: #666; font-size: 11px;")
        batch_layout.addWidget(batch_help)
        basic_layout.addRow("批次大小 (Batch Size):", batch_layout)

        # 图片尺寸
        imgsz_layout = QHBoxLayout()
        self.imgsz_spin = QSpinBox()
        self.imgsz_spin.setRange(320, 2048)
        self.imgsz_spin.setSingleStep(32)
        self.imgsz_spin.setValue(self.recommended_params.get('imgsz', 1152))
        imgsz_layout.addWidget(self.imgsz_spin)
        imgsz_help = QLabel("🖼️ 训练时图片的最大尺寸。针对720x1280图片，推荐使用1152。")
        imgsz_help.setWordWrap(True)
        imgsz_help.setStyleSheet("color: #666; font-size: 11px;")
        imgsz_layout.addWidget(imgsz_help)
        basic_layout.addRow("图片尺寸 (Image Size):", imgsz_layout)

        # 工作线程数
        workers_layout = QHBoxLayout()
        self.workers_spin = QSpinBox()
        self.workers_spin.setRange(1, 32)
        self.workers_spin.setValue(self.recommended_params['workers'])
        workers_layout.addWidget(self.workers_spin)
        workers_help = QLabel("🔧 并行加载数据的线程数。影响数据加载速度和GPU利用率。")
        workers_help.setWordWrap(True)
        workers_help.setStyleSheet("color: #666; font-size: 11px;")
        workers_layout.addWidget(workers_help)
        basic_layout.addRow("工作线程数 (Workers):", workers_layout)
        main_layout.addWidget(basic_group)

        # --- 显存优化建议组 ---
        optimization_group = QGroupBox("🚀 显存优化建议")
        optimization_layout = QVBoxLayout(optimization_group)
        
        # 显存优化按钮
        optimize_layout = QHBoxLayout()
        self.optimize_btn = QPushButton("一键优化参数 (12G显存)")
        self.optimize_btn.setToolTip("根据12G显存自动优化训练参数，提高GPU利用率")
        self.optimize_btn.clicked.connect(self.optimize_for_12g_gpu)
        optimize_layout.addWidget(self.optimize_btn)
        
        # 当前显存利用率提示
        gpu_info = self.system_info['gpu']
        if gpu_info['available']:
            gpu_memory_text = f"检测到 {gpu_info['name']} ({gpu_info['memory_gb']:.1f}G显存)"
            if gpu_info['memory_gb'] >= 12:
                gpu_memory_text += "\n💡 建议使用优化参数充分利用显存，提高训练效率"
            elif gpu_info['memory_gb'] >= 8:
                gpu_memory_text += "\n⚠️ 显存较少，建议谨慎调整批次大小"
            else:
                gpu_memory_text += "\n❌ 显存不足，建议降低参数或使用CPU训练"
        else:
            gpu_memory_text = "❌ 未检测到GPU，将使用CPU训练"
        
        gpu_info_label = QLabel(gpu_memory_text)
        gpu_info_label.setWordWrap(True)
        gpu_info_label.setStyleSheet("color: #666; font-size: 11px; padding: 5px;")
        optimization_layout.addWidget(gpu_info_label)
        optimization_layout.addLayout(optimize_layout)
        main_layout.addWidget(optimization_group)

        # --- 高级参数组 ---
        advanced_group = QGroupBox("🚀 高级参数")
        advanced_layout = QFormLayout(advanced_group)
        advanced_layout.setSpacing(10)

        # 学习率
        lr_layout = QHBoxLayout()
        self.lr_spin = QDoubleSpinBox()
        self.lr_spin.setRange(0.00001, 0.1)
        self.lr_spin.setDecimals(5)
        self.lr_spin.setSingleStep(0.0001)
        self.lr_spin.setValue(self.recommended_params['learning_rate'])
        lr_layout.addWidget(self.lr_spin)
        lr_help = QLabel("📈 控制模型权重更新的步长。过大会震荡，过小会收敛慢。")
        lr_help.setWordWrap(True)
        lr_help.setStyleSheet("color: #666; font-size: 11px;")
        lr_layout.addWidget(lr_help)
        advanced_layout.addRow("学习率 (Learning Rate):", lr_layout)

        # 早停耐心
        patience_layout = QHBoxLayout()
        self.patience_spin = QSpinBox()
        self.patience_spin.setRange(5, 200)
        self.patience_spin.setValue(self.recommended_params['patience'])
        patience_layout.addWidget(self.patience_spin)
        patience_help = QLabel("⏰ 连续多少轮无改善后停止训练。防止过拟合，节省训练时间。")
        patience_help.setWordWrap(True)
        patience_help.setStyleSheet("color: #666; font-size: 11px;")
        patience_layout.addWidget(patience_help)
        advanced_layout.addRow("早停耐心 (Patience):", patience_layout)

        # 训练设备
        device_layout = QHBoxLayout()
        self.device_combo = QComboBox()
        self.device_combo.addItems(['cuda', 'cpu'])
        if self.system_info['gpu']['available']:
            self.device_combo.setCurrentText('cuda')
        else:
            self.device_combo.setCurrentText('cpu')
        device_layout.addWidget(self.device_combo)
        device_help = QLabel("💻 训练设备。CUDA使用GPU速度快，CPU兼容性好但速度慢。")
        device_help.setWordWrap(True)
        device_help.setStyleSheet("color: #666; font-size: 11px;")
        device_layout.addWidget(device_help)
        advanced_layout.addRow("训练设备 (Device):", device_layout)
        main_layout.addWidget(advanced_group)

        main_layout.addStretch()
        return widget
    
    def create_help_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml("""
        <h4>🛠️ 环境准备说明</h4>
        <ol>
            <li>访问 <a href='https://developer.nvidia.com/cuda-downloads'>https://developer.nvidia.com/cuda-downloads</a></li>
            <li>Operating System 选 <b>Windows</b>，Architecture 选 <b>x86_64</b>，Version 选对应你的 <b>Win10/Win11</b>，Installer Type 选 <b>exe(local)</b></li>
            <li>安装时请选择 <b>自定义安装</b>，<span style='color:red'><b>只勾选CUDA，其他全部取消（重要！）</b></span></li>
            <li>如不需GPU加速，可跳过此步骤</li>
        </ol>
        <h4>PyTorch 安装</h4>
        <ol>
            <li>访问 <a href='https://pytorch.org/get-started/locally'>https://pytorch.org/get-started/locally</a></li>
            <li>PyTorch Build 选 <b>Stable</b>，Your OS 选 <b>Windows</b>，Package 选 <b>Pip</b>，Language 选 <b>Python</b></li>
            <li>Compute Platform 选最新的CUDA版本（如CUDA 11.8），如未安装CUDA或仅需CPU，选 <b>CPU</b></li>
            <li>复制页面下方生成的命令，在命令行中粘贴运行。例如：<br>
            <code>pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118</code><br>
            或CPU版本：<br>
            <code>pip3 install torch torchvision torchaudio</code></li>
        </ol>
        <p style='color:#888'>备注：安装CUDA时务必只勾选CUDA组件，PyTorch安装命令请以官网自动生成内容为准。针对720x1280高分辨率图片，建议使用8GB+显存的GPU。</p>
        <hr/>
        <h4>🎯 参数建议 (针对720x1280图片优化)</h4>
        <p>• <b>训练轮数：</b>{epochs} 轮 (小数据集100-300轮，大数据集30-100轮)</p>
        <p>• <b>批次大小：</b>{batch_size} (已大幅降低以防止显存不足，高分辨率图片建议≤8)</p>
        <p>• <b>图片尺寸：</b>{imgsz} (默认1152px最佳适配720x1280分辨率，根据显存自动调整)</p>
        <p>• <b>工作线程：</b>{workers} (通常设为CPU核心数的一半)</p>
        <p>• <b>学习率：</b>{lr} (大数据集用较小值，小数据集用较大值)</p>
        <p>• <b>早停耐心：</b>{patience} 轮 (小批次需要更多时间收敛，已增加耐心值)</p>
        <hr/>
        <h4>🔧 常见问题解决</h4>
        <b>❌ 显存不足 (OOM)</b><br>• 减小批次大小 (batch_size)，720x1280图片建议≤4<br>• 降低图片尺寸 (imgsz)，可尝试960或640<br>• 选择更小的模型 (yolo11n.pt 或 yolo11s.pt)<br>• 关闭其他占用显存的程序<br>• 重启电脑清理显存缓存<br><br>
        <b>📉 训练效果不佳</b><br>• 增加训练轮数 (epochs)<br>• 调整学习率 (learning_rate)<br>• 检查数据集质量和标注准确性<br>• 确保图片尺寸设置正确 (1280适配720x1280)<br><br>
        <b>⏱️ 训练速度慢</b><br>• 使用GPU训练 (cuda)<br>• 增加工作线程数 (workers)<br>• 适当增大批次大小 (但不超过显存限制)<br><br>
        <h4>📱 720x1280分辨率优化建议</h4>
        <b>🎯 推荐配置：</b><br>• 图片尺寸：1152 (最佳适配720x1280宽高比，保持原始比例)<br>• 备选尺寸：960-1280 (根据显存容量调整)<br>• 批次大小：2-4 (高分辨率图片大幅降低批次)<br>• 早停耐心：20+ (小批次需要更多训练时间)<br>• 模型选择：yolo11s.pt 或 yolo11m.pt (平衡精度与性能)<br>• 显存优化：优先降低批次大小，其次降低图片尺寸<br>
        <p><strong>1152尺寸优势：</strong></p>
        <ul>
            <li>完美适配720x1280宽高比（9:16），避免图像变形</li>
            <li>保持原始图像比例，提高检测精度</li>
            <li>相比1280尺寸节省约25%显存，降低OOM风险</li>
            <li>训练速度更快，同时保持较高的检测质量</li>
        </ul>
        <hr/>
        <h4>🔄 增量训练功能说明</h4>
        <b>📋 适用场景：</b><br>• 已有训练好的模型，需要添加少量新数据<br>• 数据集变化不大，只需微调现有模型<br>• 希望在已有模型基础上继续优化<br><br>
        <b>⚙️ 增量训练优化：</b><br>• 学习率自动降低至1/10，适合微调<br>• 训练轮数减少至1/3，避免过拟合<br>• 早停耐心增加10轮，给模型更多适应时间<br>• 自动使用项目中最新的best.pt模型作为基础<br><br>
        <b>📁 模型检测路径：</b><br>• runs/train/*/best.pt (优先，最新训练结果)<br>• runs/train/weights/*.pt (备选)<br>• 项目根目录/*.pt (排除预训练模型)<br><br>
        <b>💡 使用建议：</b><br>• 首次训练请使用普通模式<br>• 增量训练前确保已有good的基础模型<br>• 新增数据量较少时推荐使用增量训练<br>• 数据集结构变化较大时建议重新完整训练
        """.format(
            epochs=self.recommended_params['epochs'],
            batch_size=self.recommended_params['batch_size'],
            imgsz=self.recommended_params.get('imgsz', 1280),
            workers=self.recommended_params['workers'],
            lr=self.recommended_params['learning_rate'],
            patience=self.recommended_params['patience']
        ))
        layout.addWidget(help_text)
        return widget
    
    def check_trained_models(self):
        """检查项目目录下是否有已训练的模型"""
        # 检查 runs/train 目录下的 best.pt 文件
        runs_train_dir = os.path.join(self.project_root, 'runs', 'train')
        if os.path.exists(runs_train_dir):
            for root, dirs, files in os.walk(runs_train_dir):
                if 'best.pt' in files:
                    return True
        
        # 检查 runs/train/weights 目录
        weights_dir = os.path.join(self.project_root, 'runs', 'train', 'weights')
        if os.path.exists(weights_dir):
            for file in os.listdir(weights_dir):
                if file.endswith('.pt'):
                    return True
        
        # 检查项目根目录下的 .pt 文件
        for file in os.listdir(self.project_root):
            if file.endswith('.pt') and file != 'yolo11n.pt' and file != 'yolo11s.pt' and file != 'yolo11m.pt' and file != 'yolo11l.pt' and file != 'yolo11x.pt':
                return True
                
        return False
    
    def get_best_model_path(self):
        """获取最佳模型路径"""
        # 优先查找 runs/train 目录下最新的 best.pt
        runs_train_dir = os.path.join(self.project_root, 'runs', 'train')
        best_models = []
        
        if os.path.exists(runs_train_dir):
            for root, dirs, files in os.walk(runs_train_dir):
                if 'best.pt' in files:
                    model_path = os.path.join(root, 'best.pt')
                    best_models.append((model_path, os.path.getmtime(model_path)))
        
        if best_models:
            # 返回最新的模型
            best_models.sort(key=lambda x: x[1], reverse=True)
            return best_models[0][0]
        
        # 如果没找到，检查 weights 目录
        weights_dir = os.path.join(self.project_root, 'runs', 'train', 'weights')
        if os.path.exists(weights_dir):
            for file in os.listdir(weights_dir):
                if file.endswith('.pt'):
                    return os.path.join(weights_dir, file)
        
        return None
    
    def on_incremental_changed(self, state):
        """增量训练复选框状态变化时调整参数"""
        if state == 2:  # 勾选状态
            # 使用系统推荐的增量训练参数
            current_params = {
                'epochs': self.epochs_spin.value(),
                'batch_size': self.batch_size_spin.value(),
                'learning_rate': self.lr_spin.value(),
                'patience': self.patience_spin.value(),
                'workers': self.workers_spin.value(),
                'imgsz': self.imgsz_spin.value()
            }
            
            # 获取增量训练推荐参数
            incremental_params = SystemInfoDetector.recommend_incremental_training_params(current_params)
            
            # 应用增量训练参数
            self.lr_spin.setValue(incremental_params['learning_rate'])
            self.epochs_spin.setValue(incremental_params['epochs'])
            self.patience_spin.setValue(incremental_params['patience'])
            self.batch_size_spin.setValue(incremental_params['batch_size'])
            
            # 显示增量训练提示
            base_model_path = self.get_best_model_path()
            if base_model_path:
                QMessageBox.information(self, "增量训练模式", 
                    f"已启用增量训练模式！\n\n"
                    f"基础模型：{os.path.basename(base_model_path)}\n"
                    f"参数已自动优化为增量训练模式：\n"
                    f"• 学习率降低至 {incremental_params['learning_rate']:.5f}\n"
                    f"• 训练轮数调整为 {incremental_params['epochs']} 轮\n"
                    f"• 早停耐心增加至 {incremental_params['patience']} 轮\n\n"
                    f"适合在现有模型基础上添加少量新数据进行微调。")
            
        else:  # 取消勾选状态
            # 恢复为推荐参数
            self.lr_spin.setValue(self.recommended_params['learning_rate'])
            self.epochs_spin.setValue(self.recommended_params['epochs'])
            self.patience_spin.setValue(self.recommended_params['patience'])
            self.batch_size_spin.setValue(self.recommended_params['batch_size'])
    
    def optimize_for_12g_gpu(self):
        """针对12G显存优化训练参数"""
        gpu_info = self.system_info['gpu']
        
        if not gpu_info['available']:
            QMessageBox.warning(self, "优化失败", "未检测到GPU，无法进行显存优化。")
            return
        
        # 根据显存大小提供不同的优化策略
        if gpu_info['memory_gb'] >= 12:
            # 12G+显存：激进优化，充分利用显存
            optimized_params = {
                'batch_size': 16,  # 大批次充分利用显存
                'workers': 8,      # 增加数据加载线程
                'imgsz': 1152,     # 适配720x1280的最佳尺寸
                'learning_rate': 0.001,  # 大批次可以用稍高学习率
                'epochs': 150,     # 大批次收敛更快，可以减少轮数
                'patience': 25     # 相应调整早停耐心
            }
            message = f"已为您的 {gpu_info['memory_gb']:.1f}G 显存优化参数：\n\n" \
                     f"• 批次大小: {optimized_params['batch_size']} (充分利用显存)\n" \
                     f"• 工作线程: {optimized_params['workers']} (加速数据加载)\n" \
                     f"• 图片尺寸: {optimized_params['imgsz']} (适配720x1280)\n" \
                     f"• 训练轮数: {optimized_params['epochs']} (大批次收敛更快)\n\n" \
                     f"预计显存占用: 8-10G (80-85%利用率)\n" \
                     f"训练速度提升: 3-5倍"
        elif gpu_info['memory_gb'] >= 8:
            # 8-12G显存：中等优化
            optimized_params = {
                'batch_size': 8,
                'workers': 6,
                'imgsz': 1152,
                'learning_rate': 0.001,
                'epochs': 180,
                'patience': 30
            }
            message = f"已为您的 {gpu_info['memory_gb']:.1f}G 显存优化参数：\n\n" \
                     f"• 批次大小: {optimized_params['batch_size']} (中等批次)\n" \
                     f"• 工作线程: {optimized_params['workers']} (平衡性能)\n" \
                     f"• 图片尺寸: {optimized_params['imgsz']} (适配720x1280)\n" \
                     f"• 训练轮数: {optimized_params['epochs']} (适中轮数)\n\n" \
                     f"预计显存占用: 6-7G (75-85%利用率)\n" \
                     f"训练速度提升: 2-3倍"
        else:
            # 低显存：保守优化
            optimized_params = {
                'batch_size': 4,
                'workers': 4,
                'imgsz': 960,  # 降低分辨率节省显存
                'learning_rate': 0.001,
                'epochs': 200,
                'patience': 35
            }
            message = f"已为您的 {gpu_info['memory_gb']:.1f}G 显存优化参数：\n\n" \
                     f"• 批次大小: {optimized_params['batch_size']} (小批次)\n" \
                     f"• 工作线程: {optimized_params['workers']} (适中线程)\n" \
                     f"• 图片尺寸: {optimized_params['imgsz']} (节省显存)\n" \
                     f"• 训练轮数: {optimized_params['epochs']} (更多轮数)\n\n" \
                     f"预计显存占用: 4-5G (70-80%利用率)\n" \
                     f"训练速度提升: 1.5-2倍"
        
        # 确认对话框
        reply = QMessageBox.question(self, "确认优化参数", 
            f"{message}\n\n是否应用这些优化参数？", 
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            # 应用优化参数
            self.batch_size_spin.setValue(optimized_params['batch_size'])
            self.workers_spin.setValue(optimized_params['workers'])
            self.imgsz_spin.setValue(optimized_params['imgsz'])
            self.lr_spin.setValue(optimized_params['learning_rate'])
            self.epochs_spin.setValue(optimized_params['epochs'])
            self.patience_spin.setValue(optimized_params['patience'])
            
            QMessageBox.information(self, "优化完成", 
                "参数优化完成！\n\n" \
                "💡 提示：\n" \
                "• 如果训练中出现显存不足，请适当降低批次大小\n" \
                "• 监控GPU利用率，理想状态应保持在90-100%\n" \
                "• 显存占用应达到80-85%以获得最佳性能")

    def start_training(self):
        # 1. 检查 ultralytics 依赖
        try:
            import ultralytics
        except ImportError:
            reply = QMessageBox.question(self, "缺少依赖", "需要安装 ultralytics 库才能进行训练。\n是否现在为您自动安装？", 
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.Yes:
                self.install_ultralytics()
            else:
                QMessageBox.critical(self, "错误", "缺少 ultralytics 库，训练已取消。")
            return
        # 2. 验证项目数据
        if not self.validate_project_data():
            return
        
        # 2.5. 验证增量训练设置
        if self.incremental_checkbox.isChecked():
            base_model_path = self.get_best_model_path()
            if not base_model_path or not os.path.exists(base_model_path):
                QMessageBox.warning(self, "增量训练错误", 
                    "启用了增量训练但未找到可用的基础模型！\n\n"
                    "请确保项目目录下存在已训练的模型文件：\n"
                    "• runs/train/*/best.pt\n"
                    "• runs/train/weights/*.pt\n"
                    "• 项目根目录/*.pt\n\n"
                    "建议先进行完整训练，或取消增量训练选项。")
                return
            
            # 确认增量训练
            reply = QMessageBox.question(self, "确认增量训练", 
                f"即将开始增量训练：\n\n"
                f"基础模型：{os.path.basename(base_model_path)}\n"
                f"训练轮数：{self.epochs_spin.value()} 轮\n"
                f"学习率：{self.lr_spin.value():.5f}\n\n"
                f"增量训练将在现有模型基础上继续训练，\n"
                f"适合添加少量新数据的场景。\n\n"
                f"是否继续？", 
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply != QMessageBox.StandardButton.Yes:
                return
        # 3. 获取参数
        params = {
            'epochs': self.epochs_spin.value(),
            'batch_size': self.batch_size_spin.value(),
            'learning_rate': self.lr_spin.value(),
            'patience': self.patience_spin.value(),
            'workers': self.workers_spin.value(),
            'device': self.device_combo.currentText(),
            'model_weight': self.model_combo.currentText().split()[0],  # 取权重文件名
            'imgsz': self.imgsz_spin.value(),  # 添加图片尺寸参数
            'incremental': self.incremental_checkbox.isChecked(),  # 增量训练标志
            'base_model_path': self.get_best_model_path() if self.incremental_checkbox.isChecked() else None  # 基础模型路径
        }
        # 4. 创建训练脚本
        # 复用 TrainingThread 的 create_training_script 静态用法
        train_thread = TrainingThread(self.project_root, params)
        train_script = train_thread.create_training_script()
        # 5. 打开CMD窗口运行训练
        python_exe = sys.executable
        cmd = f'start cmd /k "{python_exe} {train_script}"'
        subprocess.Popen(cmd, shell=True, cwd=self.project_root)
        # 6. 自动关闭弹窗
        self.accept()

    def validate_project_data(self):
        try:
            self.project_manager.prepare_for_training()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"准备训练数据时出错: {e}")
            return False
        is_valid, message = self.project_manager.validate_yolo_files()
        if not is_valid:
            QMessageBox.warning(self, "数据校验失败", message)
            return False
        return True

    def stop_training(self):
        if self.training_thread and self.training_thread.isRunning():
            self.training_thread.stop()

    def update_progress(self, message):
        pass
    
    def on_training_finished(self, success, message, best_model_path):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        # 训练结束后的核心交互逻辑
        if success:
            dialog = QMessageBox(self)
            dialog.setWindowTitle("训练完成")
            dialog.setIcon(QMessageBox.Icon.Information)
            text = f"训练成功完成！\n\n"
            if best_model_path:
                text += f"性能最优的模型已保存至:\n<b>{best_model_path}</b>\n\n"
                text += "请在主界面的【加载模型】中选择此 'best.pt' 文件进行自动标注。"
                dialog.setDetailedText(best_model_path) # 可以展开查看完整路径
            else:
                text += "未能自动定位到 best.pt 模型，请手动在项目文件夹的 'runs/train' 目录中查找。"
            dialog.setText(text)
            open_folder_btn = dialog.addButton("打开结果文件夹", QDialogButtonBox.ActionRole)
            ok_btn = dialog.addButton("好的", QDialogButtonBox.AcceptRole)
            dialog.exec()
            if dialog.clickedButton() == open_folder_btn:
                folder_to_open = os.path.dirname(best_model_path) if best_model_path and os.path.exists(best_model_path) else os.path.join(self.project_root, 'runs')
                if os.path.exists(folder_to_open):
                    QDesktopServices.openUrl(QUrl.fromLocalFile(folder_to_open))
                else:
                    QMessageBox.warning(self, "错误", f"文件夹不存在: {folder_to_open}")
        else:
            QMessageBox.critical(self, "训练失败", message)
    
    def install_ultralytics(self):
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "install", "ultralytics"], capture_output=True, text=True, check=True)
            QMessageBox.information(self, "成功", "ultralytics 安装成功，请重新开始训练。")
        except subprocess.CalledProcessError as e:
            QMessageBox.critical(self, "失败", f"安装失败: \n{e.stderr}")
            
    def closeEvent(self, event):
        """关闭窗口时，确保训练线程已停止"""
        if self.training_thread and self.training_thread.isRunning():
            self.stop_training()
            self.training_thread.wait() # 等待线程完全结束
        super().closeEvent(event)