        function fetchData() {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'http://127.0.0.1:8082/MyWcfService/getstring?duankou=COM3&amp;hco=0&amp;daima=0', true);
            xhr.onreadystatechange = function () {
                if (xhr.readyState == 4 &amp;&amp; xhr.status == 200) {
                    console.log(xhr.responseText);
                } else if (xhr.status != 200) {
                    console.error('Error:', xhr.status, xhr.statusText);
                }
            };
            xhr.send();
        }