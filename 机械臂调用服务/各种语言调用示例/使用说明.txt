如果正常打开端口会返回一个资源号（大于1的数），保存此值后续要用到！！！！！！
打开端口后一定要延迟2秒左右等机器准备好再发送移动或点击指令！！！！！！！！
建议每条操作指令后面都写一条延时指令让机器执行完毕再发下一条指令，否则指令可能会丢失！！！

打开COM4端口，返回字符型资源号（大于1的数），端口不存在或被占用返回0  ：
http://127.0.0.1:8082/MyWcfService/getstring?duankou=COM4&hco=0&daima=0 

移动到X50Y50处：
http://127.0.0.1:8082/MyWcfService/getstring?duankou=0&hco=资源号&daima=X50Y50

触控笔下降6mm：
http://127.0.0.1:8082/MyWcfService/getstring?duankou=0&hco=资源号&daima=Z6

触控笔抬起：
http://127.0.0.1:8082/MyWcfService/getstring?duankou=0&hco=资源号&daima=Z0

关闭端口 （脚本结束后关闭，中途无需关闭,）
http://127.0.0.1:8082/MyWcfService/getstring?duankou=0&hco=资源号&daima=X0Y0Z0       注释：机器复位
http://127.0.0.1:8082/MyWcfService/getstring?duankou=0&hco=资源号&daima=0                 注释：关闭端口
