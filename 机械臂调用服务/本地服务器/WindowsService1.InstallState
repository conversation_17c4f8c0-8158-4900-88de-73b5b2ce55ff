<?xml version="1.0" encoding="utf-8"?><ArrayOfKeyValueOfanyTypeanyType xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns:x="http://www.w3.org/2001/XMLSchema" z:Id="1" z:Type="System.Collections.Hashtable" z:Assembly="0" xmlns:z="http://schemas.microsoft.com/2003/10/Serialization/" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><LoadFactor z:Id="2" z:Type="System.Single" z:Assembly="0" xmlns="">0.72</LoadFactor><Version z:Id="3" z:Type="System.Int32" z:Assembly="0" xmlns="">2</Version><Comparer i:nil="true" xmlns="" /><HashCodeProvider i:nil="true" xmlns="" /><HashSize z:Id="4" z:Type="System.Int32" z:Assembly="0" xmlns="">3</HashSize><Keys z:Id="5" z:Type="System.Object[]" z:Assembly="0" z:Size="2" xmlns=""><anyType z:Id="6" z:Type="System.String" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">_reserved_nestedSavedStates</anyType><anyType z:Id="7" z:Type="System.String" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">_reserved_lastInstallerAttempted</anyType></Keys><Values z:Id="8" z:Type="System.Object[]" z:Assembly="0" z:Size="2" xmlns=""><anyType z:Id="9" z:Type="System.Collections.IDictionary[]" z:Assembly="0" z:Size="1" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><ArrayOfKeyValueOfanyTypeanyType z:Id="10" z:Type="System.Collections.Hashtable" z:Assembly="0"><LoadFactor z:Id="11" z:Type="System.Single" z:Assembly="0" xmlns="">0.72</LoadFactor><Version z:Id="12" z:Type="System.Int32" z:Assembly="0" xmlns="">2</Version><Comparer i:nil="true" xmlns="" /><HashCodeProvider i:nil="true" xmlns="" /><HashSize z:Id="13" z:Type="System.Int32" z:Assembly="0" xmlns="">3</HashSize><Keys z:Id="14" z:Type="System.Object[]" z:Assembly="0" z:Size="2" xmlns=""><anyType z:Ref="6" i:nil="true" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /><anyType z:Ref="7" i:nil="true" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /></Keys><Values z:Id="15" z:Type="System.Object[]" z:Assembly="0" z:Size="2" xmlns=""><anyType z:Id="16" z:Type="System.Collections.IDictionary[]" z:Assembly="0" z:Size="2" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><ArrayOfKeyValueOfanyTypeanyType z:Id="17" z:Type="System.Collections.Hashtable" z:Assembly="0"><LoadFactor z:Id="18" z:Type="System.Single" z:Assembly="0" xmlns="">0.72</LoadFactor><Version z:Id="19" z:Type="System.Int32" z:Assembly="0" xmlns="">4</Version><Comparer i:nil="true" xmlns="" /><HashCodeProvider i:nil="true" xmlns="" /><HashSize z:Id="20" z:Type="System.Int32" z:Assembly="0" xmlns="">7</HashSize><Keys z:Id="21" z:Type="System.Object[]" z:Assembly="0" z:Size="3" xmlns=""><anyType z:Id="22" z:Type="System.String" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">installed</anyType><anyType z:Ref="6" i:nil="true" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /><anyType z:Ref="7" i:nil="true" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /></Keys><Values z:Id="23" z:Type="System.Object[]" z:Assembly="0" z:Size="3" xmlns=""><anyType z:Id="24" z:Type="System.Boolean" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">true</anyType><anyType z:Id="25" z:Type="System.Collections.IDictionary[]" z:Assembly="0" z:Size="1" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><ArrayOfKeyValueOfanyTypeanyType z:Id="26" z:Type="System.Collections.Hashtable" z:Assembly="0"><LoadFactor z:Id="27" z:Type="System.Single" z:Assembly="0" xmlns="">0.72</LoadFactor><Version z:Id="28" z:Type="System.Int32" z:Assembly="0" xmlns="">6</Version><Comparer i:nil="true" xmlns="" /><HashCodeProvider i:nil="true" xmlns="" /><HashSize z:Id="29" z:Type="System.Int32" z:Assembly="0" xmlns="">7</HashSize><Keys z:Id="30" z:Type="System.Object[]" z:Assembly="0" z:Size="5" xmlns=""><anyType z:Id="31" z:Type="System.String" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">logExists</anyType><anyType z:Ref="6" i:nil="true" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /><anyType z:Ref="7" i:nil="true" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /><anyType z:Id="32" z:Type="System.String" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">alreadyRegistered</anyType><anyType z:Id="33" z:Type="System.String" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">baseInstalledAndPlatformOK</anyType></Keys><Values z:Id="34" z:Type="System.Object[]" z:Assembly="0" z:Size="5" xmlns=""><anyType z:Id="35" z:Type="System.Boolean" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">true</anyType><anyType z:Id="36" z:Type="System.Collections.IDictionary[]" z:Assembly="0" z:Size="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /><anyType z:Id="37" z:Type="System.Int32" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">-1</anyType><anyType z:Id="38" z:Type="System.Boolean" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">true</anyType><anyType z:Id="39" z:Type="System.Boolean" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">true</anyType></Values></ArrayOfKeyValueOfanyTypeanyType></anyType><anyType z:Id="40" z:Type="System.Int32" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">0</anyType></Values></ArrayOfKeyValueOfanyTypeanyType><ArrayOfKeyValueOfanyTypeanyType z:Id="41" z:Type="System.Collections.Hashtable" z:Assembly="0"><LoadFactor z:Id="42" z:Type="System.Single" z:Assembly="0" xmlns="">0.72</LoadFactor><Version z:Id="43" z:Type="System.Int32" z:Assembly="0" xmlns="">4</Version><Comparer i:nil="true" xmlns="" /><HashCodeProvider i:nil="true" xmlns="" /><HashSize z:Id="44" z:Type="System.Int32" z:Assembly="0" xmlns="">7</HashSize><Keys z:Id="45" z:Type="System.Object[]" z:Assembly="0" z:Size="3" xmlns=""><anyType z:Ref="6" i:nil="true" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /><anyType z:Id="46" z:Type="System.String" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">Account</anyType><anyType z:Ref="7" i:nil="true" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /></Keys><Values z:Id="47" z:Type="System.Object[]" z:Assembly="0" z:Size="3" xmlns=""><anyType z:Id="48" z:Type="System.Collections.IDictionary[]" z:Assembly="0" z:Size="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /><anyType z:Id="49" z:Type="System.ServiceProcess.ServiceAccount" z:Assembly="System.ServiceProcess, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">LocalSystem</anyType><anyType z:Id="50" z:Type="System.Int32" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">-1</anyType></Values></ArrayOfKeyValueOfanyTypeanyType></anyType><anyType z:Id="51" z:Type="System.Int32" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">1</anyType></Values></ArrayOfKeyValueOfanyTypeanyType></anyType><anyType z:Id="52" z:Type="System.Int32" z:Assembly="0" xmlns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">0</anyType></Values></ArrayOfKeyValueOfanyTypeanyType>