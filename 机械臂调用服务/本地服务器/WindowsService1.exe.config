<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
    </startup>
	<system.serviceModel>

		<services>

		
			<service name="WindowsService1.MyService">
				<host>
					<baseAddresses>
						<add baseAddress="http://127.0.0.1:8082/MyWcfService"/>
					</baseAddresses>
				</host>
				<endpoint address="" binding="webHttpBinding" contract="WindowsService1.IMyService" behaviorConfiguration="web"/>
			</service>
		</services>
		<behaviors>
			<endpointBehaviors>
				<behavior name="web">
					<webHttp/>
				</behavior>
			</endpointBehaviors>
		</behaviors>

	</system.serviceModel>

</configuration>